"""
Database Manager

Handles database connections and patent data lookups for legal opinion processing.
Uses existing database connection utilities and integrates with USPTO Patent API.
"""

import os
import json
import asyncio
from typing import Optional, Dict, List, Any, Tuple
from pathlib import Path

# Import existing database connection utilities
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from AI.USPTO_Patent_API import PatentApi
from logdata import log_message

from .patent_utils import normalize_patent_number, is_design_patent


class DatabaseManager:
    """
    Manages database operations and patent data retrieval for legal opinion processing.
    """
    
    def __init__(self):
        """Initialize the database manager."""
        self.patent_api = None
    
    async def get_patent_api(self) -> PatentApi:
        """
        Get or create a PatentApi instance with active session.
        
        Returns:
            PatentApi instance with active session
        """
        if self.patent_api is None:
            self.patent_api = PatentApi()
            await self.patent_api.start_session()
        elif self.patent_api.session is None or self.patent_api.session.closed:
            await self.patent_api.start_session()
        
        return self.patent_api
    
    async def close_patent_api(self):
        """Close the patent API session."""
        if self.patent_api:
            await self.patent_api.close_session()
            self.patent_api = None
    
    def lookup_patent_by_reg_no(self, reg_no: str) -> Optional[Dict[str, Any]]:
        """
        Look up patent data in the database by registration number.
        
        Args:
            reg_no: Normalized patent registration number (e.g., "D640227")
            
        Returns:
            Patent data dictionary or None if not found
        """
        try:
            conn = get_db_connection()
            if not conn:
                log_message("Failed to get database connection", level='ERROR')
                return None
            
            cursor = conn.cursor()
            
            # Use parameterized query for security
            query = "SELECT * FROM public.patents WHERE reg_no = %s LIMIT 1;"
            cursor.execute(query, (reg_no,))
            
            result = cursor.fetchone()
            
            if result:
                # Get column names
                columns = [desc[0] for desc in cursor.description]
                # Convert to dictionary
                patent_data = dict(zip(columns, result))
                
                log_message(f"Found patent data for reg_no: {reg_no}", level='INFO')
                return patent_data
            else:
                log_message(f"No patent data found for reg_no: {reg_no}", level='INFO')
                return None
                
        except Exception as e:
            log_message(f"Error looking up patent {reg_no}: {e}", level='ERROR')
            return None
        finally:
            if 'conn' in locals() and conn:
                conn.close()
    
    async def download_patent_assets(self, document_id: str, output_dir: str, 
                                   filename: Optional[str] = None, 
                                   formats: Optional[List[str]] = None) -> Dict[str, str]:
        """
        Download patent assets using the USPTO Patent API.
        
        Args:
            document_id: Patent document ID from database
            output_dir: Directory to save files
            filename: Optional custom filename (defaults to document_id)
            formats: List of formats to download (defaults to ["pdf"])
            
        Returns:
            Dictionary with paths to downloaded files
        """
        if formats is None:
            formats = ["pdf"]
        
        try:
            api = await self.get_patent_api()
            
            # Create output directory
            os.makedirs(output_dir, exist_ok=True)
            
            # Download using existing PatentApi.save_patent method
            result = await api.save_patent(
                document_id=document_id,
                output_dir=output_dir,
                filename=filename,
                formats=formats
            )
            
            log_message(f"Downloaded patent assets for document_id: {document_id}", level='INFO')
            return result
            
        except Exception as e:
            log_message(f"Error downloading patent assets for {document_id}: {e}", level='ERROR')
            return {}
    
    def save_patent_metadata(self, output_dir: str, patent_data: Dict[str, Any], 
                           filename: str = "db-metadata") -> str:
        """
        Save patent database metadata as JSON file.
        
        Args:
            output_dir: Directory to save metadata
            patent_data: Patent data dictionary from database
            filename: Filename without extension (defaults to "db-metadata")
            
        Returns:
            Path to saved metadata file
        """
        try:
            os.makedirs(output_dir, exist_ok=True)
            
            metadata_path = os.path.join(output_dir, f"{filename}.json")
            
            # Convert any non-serializable objects to strings
            serializable_data = {}
            for key, value in patent_data.items():
                try:
                    json.dumps(value)  # Test if serializable
                    serializable_data[key] = value
                except (TypeError, ValueError):
                    serializable_data[key] = str(value)
            
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, indent=2, default=str)
            
            log_message(f"Saved patent metadata to: {metadata_path}", level='INFO')
            return metadata_path
            
        except Exception as e:
            log_message(f"Error saving patent metadata: {e}", level='ERROR')
            return ""
    
    async def process_asserted_patent(self, patent_number: str, output_dir: str) -> Dict[str, Any]:
        """
        Process the asserted patent: normalize, lookup, and download assets.
        
        Args:
            patent_number: Raw patent number from form
            output_dir: Directory to save patent assets
            
        Returns:
            Dictionary with processing results:
            {
                "success": bool,
                "reg_no": str,
                "db_status": str,  # "found" or "missing"
                "patent_data": dict or None,
                "downloaded_files": dict,
                "metadata_path": str
            }
        """
        result = {
            "success": False,
            "reg_no": "",
            "db_status": "missing",
            "patent_data": None,
            "downloaded_files": {},
            "metadata_path": ""
        }
        
        try:
            # Step 1: Normalize patent number
            reg_no = normalize_patent_number(patent_number)
            result["reg_no"] = reg_no
            
            if not reg_no:
                log_message(f"Could not normalize patent number: {patent_number}", level='ERROR')
                return result
            
            # Step 2: Database lookup
            patent_data = self.lookup_patent_by_reg_no(reg_no)
            
            if patent_data:
                result["db_status"] = "found"
                result["patent_data"] = patent_data
                
                # Step 3: Download patent assets if document_id is available
                document_id = patent_data.get('document_id')
                if document_id:
                    downloaded_files = await self.download_patent_assets(
                        document_id=document_id,
                        output_dir=output_dir,
                        formats=["pdf"]
                    )
                    result["downloaded_files"] = downloaded_files
                
                # Step 4: Save metadata
                metadata_path = self.save_patent_metadata(output_dir, patent_data)
                result["metadata_path"] = metadata_path
                
                result["success"] = True
                log_message(f"Successfully processed asserted patent: {reg_no}", level='INFO')
            else:
                # Patent not found in database - still mark as processed but missing
                result["db_status"] = "missing"
                
                # Save minimal metadata indicating missing status
                missing_metadata = {
                    "reg_no": reg_no,
                    "original_input": patent_number,
                    "db_status": "missing",
                    "processed_at": str(asyncio.get_event_loop().time())
                }
                metadata_path = self.save_patent_metadata(output_dir, missing_metadata)
                result["metadata_path"] = metadata_path
                
                result["success"] = True  # Still successful processing, just missing data
                log_message(f"Processed asserted patent (missing from DB): {reg_no}", level='WARNING')
            
        except Exception as e:
            log_message(f"Error processing asserted patent {patent_number}: {e}", level='ERROR')
            result["success"] = False
        
        return result
    
    async def process_client_patents(self, client_patents: List[Dict[str, Any]], 
                                   base_output_dir: str) -> List[Dict[str, Any]]:
        """
        Process client-owned patents (US only for database lookup).
        
        Args:
            client_patents: List of client patent dictionaries from form
            base_output_dir: Base directory for client patents
            
        Returns:
            List of processing results for each patent
        """
        results = []
        
        for patent_info in client_patents:
            country = patent_info.get('country', '').upper()
            patent_number = patent_info.get('patentNumber', '')
            
            # Only process US patents for database lookup
            if country in ['US', 'USA']:
                reg_no = normalize_patent_number(patent_number)
                if reg_no:
                    # Create subdirectory for this patent
                    patent_output_dir = os.path.join(base_output_dir, reg_no)
                    
                    # Process similar to asserted patent
                    result = await self.process_asserted_patent(patent_number, patent_output_dir)
                    result["country"] = country
                    result["original_patent_info"] = patent_info
                    results.append(result)
                else:
                    log_message(f"Could not normalize client patent: {patent_number}", level='WARNING')
            else:
                # Non-US patent - save uploaded file and metadata only
                result = {
                    "success": True,
                    "reg_no": patent_info.get('patentNumber', ''),
                    "country": country,
                    "db_status": "non_us",
                    "patent_data": None,
                    "downloaded_files": {},
                    "metadata_path": "",
                    "original_patent_info": patent_info
                }
                
                # Create subdirectory using original patent number
                patent_output_dir = os.path.join(base_output_dir, patent_info.get('patentNumber', 'unknown'))
                os.makedirs(patent_output_dir, exist_ok=True)
                
                # Save metadata for non-US patent
                non_us_metadata = {
                    "reg_no": patent_info.get('patentNumber', ''),
                    "country": country,
                    "date": patent_info.get('date', ''),
                    "db_status": "non_us",
                    "processed_at": str(asyncio.get_event_loop().time())
                }
                metadata_path = self.save_patent_metadata(patent_output_dir, non_us_metadata)
                result["metadata_path"] = metadata_path
                
                results.append(result)
                log_message(f"Processed non-US client patent: {patent_number} ({country})", level='INFO')
        
        return results
