import os
from datetime import timedelta
from celery.schedules import crontab

class CeleryConfig:
    # Celery settings
    broker_url = os.environ.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    result_backend = os.environ.get('CELERY_RESULT_BACKEND', 'redis://localhost:6379/0')
    
    # Timezone settings
    timezone = 'America/New_York'
    enable_utc = True
    
    # Task serialization
    task_serializer = 'json'
    accept_content = ['json']
    result_serializer = 'json'
    result_expires = 604800 # 7 days
    
    # Worker settings
    worker_prefetch_multiplier = 1
    task_acks_late = True
    
    # Beat scheduler
    beat_scheduler = 'celery.beat:PersistentScheduler'
    
    # Beat schedule for periodic tasks
    beat_schedule = {
    'daily-case-fetch': {
        'task': 'daily_case_fetch_task',
        'schedule': crontab(hour=0, minute=30),
        'options': {'timezone': 'America/New_York'}
    },
    'weekly-case-fetch': {
        'task': 'weekly_case_fetch_task',
        'schedule': crontab(hour=1, minute=30, day_of_week=6),
        'options': {'timezone': 'America/New_York'}
    },
    'monthly-case-fetch-1': {
        'task': 'monthly_case_fetch_1_task',
        'schedule': crontab(hour=1, minute=30, day_of_week=0, day_of_month='1-7'),
        'options': {'timezone': 'America/New_York'}
    },
    'monthly-case-fetch-2': {
        'task': 'monthly_case_fetch_2_task',
        'schedule': crontab(hour=1, minute=30, day_of_week=0, day_of_month='8-14'),
        'options': {'timezone': 'America/New_York'}
    },
    'monthly-case-fetch-3': {
        'task': 'monthly_case_fetch_3_task',
        'schedule': crontab(hour=1, minute=30, day_of_week=0, day_of_month='15-21'),
        'options': {'timezone': 'America/New_York'}
    },
    # 'monthly-case-fetch-4': {
    #     'task': 'monthly_case_fetch_4_task',
    #     'schedule': crontab(hour=1, minute=30, day_of_week=0, day_of_month='22-28'),
    #     'options': {'timezone': 'America/New_York'}
    # },
}
