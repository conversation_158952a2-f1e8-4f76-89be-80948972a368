import os
from arq.connections import RedisSettings

class ARQConfig:
    # Redis settings for ARQ
    redis_settings = RedisSettings(
        host=os.environ.get('REDIS_HOST', 'localhost'),
        port=int(os.environ.get('REDIS_PORT', 6379)),
        database=int(os.environ.get('REDIS_DB', 0))
    )
    
    # Worker settings
    job_timeout = 86400  # 24 hour
    keep_result = 86400  # Keep results for 24 hours
    max_jobs = 10
    
    # Queue name
    queue_name = 'scheduler_queue'