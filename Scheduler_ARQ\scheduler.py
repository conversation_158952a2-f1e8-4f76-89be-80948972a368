import asyncio
from datetime import datetime, time
from arq import create_pool
from Scheduler_ARQ.config import ARQConfig
import logging

logger = logging.getLogger(__name__)

class ARQScheduler:
    def __init__(self):
        self.pool = None
        self.running = False
    
    async def start(self):
        """Start the ARQ scheduler"""
        self.pool = await create_pool(ARQConfig.redis_settings)
        self.running = True
        logger.info("ARQ Scheduler started")
        
        # Start scheduling tasks
        await asyncio.gather(
            self.schedule_daily_tasks(),
            self.schedule_maijiazhichi_tasks()
        )
    
    async def stop(self):
        """Stop the scheduler"""
        self.running = False
        if self.pool:
            await self.pool.close()
        logger.info("ARQ Scheduler stopped")
    
    async def schedule_daily_tasks(self):
        """Schedule USPTO trademark daily tasks"""
        while self.running:
            now = datetime.now()
            # Schedule at 23:30 EST (same as original Celery config)
            target_time = now.replace(hour=23, minute=30, second=0, microsecond=0)
            
            if now > target_time:
                # If past today's time, schedule for tomorrow
                target_time = target_time.replace(day=target_time.day + 1)
            
            wait_seconds = (target_time - now).total_seconds()
            await asyncio.sleep(wait_seconds)
            
            if self.running:
                await self.pool.enqueue_job('trademark_uspto_daily_task')
                logger.info("Scheduled USPTO trademark daily task")
                
                # Wait 24 hours for next execution
                await asyncio.sleep(86400)
    
    async def schedule_maijiazhichi_tasks(self):
        """Schedule Maijiazhichi scraper tasks"""
        while self.running:
            now = datetime.now()
            # Schedule at 03:30 EST (same as original Celery config)
            target_time = now.replace(hour=3, minute=30, second=0, microsecond=0)
            
            if now > target_time:
                # If past today's time, schedule for tomorrow
                target_time = target_time.replace(day=target_time.day + 1)
            
            wait_seconds = (target_time - now).total_seconds()
            await asyncio.sleep(wait_seconds)
            
            if self.running:
                await self.pool.enqueue_job('maijiazhichi_scraper_task')
                logger.info("Scheduled Maijiazhichi scraper task")
                
                # Wait 24 hours for next execution
                await asyncio.sleep(86400)

async def run_scheduler():
    """Main function to run the scheduler"""
    scheduler = ARQScheduler()
    try:
        await scheduler.start()
    except KeyboardInterrupt:
        logger.info("Received interrupt signal")
    finally:
        await scheduler.stop()

if __name__ == "__main__":
    asyncio.run(run_scheduler())