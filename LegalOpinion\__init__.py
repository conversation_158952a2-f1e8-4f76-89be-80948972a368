"""
LegalOpinion Backend Module

This module provides the backend functionality for legal opinion processing,
including submission workspace management, patent processing, and evidence handling.
"""

from .submission_workspace import SubmissionWorkspaceManager
from .patent_processor import PatentProcessor
from .evidence_manager import EvidenceManager
from .form_processor import LegalOpinionFormProcessor
from .database_manager import DatabaseManager
from .figure_manager import FigureManager
from .prior_art_processor import PriorArtProcessor
from .patent_utils import normalize_patent_number, validate_patent_number_format, compare_patent_dates

__version__ = "1.0.0"
__all__ = [
    "SubmissionWorkspaceManager",
    "PatentProcessor",
    "EvidenceManager",
    "LegalOpinionFormProcessor",
    "DatabaseManager",
    "FigureManager",
    "PriorArtProcessor",
    "normalize_patent_number",
    "validate_patent_number_format",
    "compare_patent_dates"
]
