{% include 'common/api_studio_header.html' %}
<link rel="stylesheet" href="{{ url_path_for('static', path='legal_opinion/legal_opinion.css') }}">
<script src="https://unpkg.com/htmx.org@1.9.10"></script>
<script src="https://unpkg.com/htmx.org/dist/ext/json-enc.js"></script>
<script src="{{ url_path_for('static', path='legal_opinion/legal_opinion.js') }}"></script>
<script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>

<div class="legal-opinion-container" x-data="legalOpinion" x-init="init()">
    <!-- Progress Indicator -->
    <div class="progress-indicator" x-show="Object.values(sections).some(s => s.completed)">
        <h4 style="color: #ffffff; margin-bottom: 12px;">Progress</h4>
        <template x-for="(section, key) in sections" :key="key">
            <div class="progress-item">
                <div class="progress-icon"
                     :class="section.completed ? 'completed' : (section.expanded ? 'current' : 'pending')">
                    <span x-text="section.completed ? '✓' : (section.expanded ? '•' : '')"></span>
                </div>
                <a href="#" @click.prevent="gotoSection(key)" class="progress-link">
                    <span x-text="getSectionTitle(key)"></span>
                </a>
            </div>
        </template>
    </div>

    <!-- Autosave Status -->
    <div class="autosave-status" style="position: fixed; top: 70px; left: 20px; z-index: 1000;">
        <span x-show="autosaveStatus === 'saving'" style="color: #FFC107;">Saving...</span>
        <span x-show="autosaveStatus === 'saved'" style="color: #1E2442;">✓ Saved</span>
        <span x-show="autosaveStatus === 'error'" style="color: #f44336;">⚠ Save failed</span>
    </div>

    <h1 style="color: #ffffff; text-align: center; margin-bottom: 30px;">Legal Opinion Intake Form</h1>
    
    <form @submit.prevent="submitForm()" x-data="{ showGlossary: false }">
        
        <!-- Privacy Banner -->
        <div style="background: rgba(30, 36, 66, 0.1); border: 1px solid #1E2442; border-radius: 8px; padding: 16px; margin-bottom: 24px;">
            <p style="color: #1E2442; margin: 0; text-align: center;">
                <strong>Attorney-Client Privileged & Confidential</strong><br>
                This information is provided for legal advice and is protected by attorney-client privilege.
            </p>
        </div>

        <!-- Section 1: Complaint Details -->
        <div id="complaint" class="form-section" :class="{ 'completed': sections.complaint.completed }">
            <div class="section-header" @click="toggleSection('complaint')">
                <h3>
                    <span class="section-number">1</span>
                    Complaint Details
                </h3>
                <button type="button" class="section-toggle" :class="{ 'expanded': sections.complaint.expanded }">
                    <span x-text="sections.complaint.expanded ? '−' : '+'"></span>
                </button>
            </div>

            <div class="section-content" x-show="sections.complaint.expanded" x-transition>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 20px; font-size: 0.9em;">
                    Tell us about the complaint or violation you received from the marketplace and basic client information.
                </p>

                <!-- Store Name -->
                <div class="form-group">
                    <label for="storeName">
                        Store Name <span class="required">*</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('storeName', $event)" tabindex="0" @keydown.enter="toggleTooltip('storeName', $event)">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'storeName' }">
                                <h4>What & Where</h4>
                                <p>The seller's store name as it appears on the marketplace (e.g., their Amazon storefront name).</p>
                                <h4>Why</h4>
                                <p>This helps identify the specific seller account and storefront involved in the complaint.</p>
                            </div>
                        </div>
                    </label>
                    <input type="text"
                           id="storeName"
                           x-model="formData.storeName"
                           hx-post="/legal_opinion/validate_field"
                           hx-trigger="change delay:500ms"
                           hx-ext="json-enc"
                           hx-vals='{"field": "storeName", "value": this.value}'
                           placeholder="Enter store name (2-120 characters, no emoji)"
                           maxlength="120"
                           required>
                    <div class="validation-message" id="storeName-validation"></div>
                </div>

                <!-- Platform -->
                <div class="form-group">
                    <label for="platform">
                        Platform <span class="required">*</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('platform', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'platform' }">
                                <h4>What & Where</h4>
                                <p>Where the complaint happened (Amazon, eBay, Etsy, Walmart…). If Amazon, enable ASIN, AHR, Case/Notice fields.</p>
                                <h4>Why</h4>
                                <p>Each marketplace has different appeal packaging; Amazon needs ASIN/AHR specifics.</p>
                            </div>
                        </div>
                    </label>
                    <select id="platform" x-model="formData.platform" required>
                        <option value="">Select platform</option>
                        <option value="amazon">Amazon</option>
                        <option value="ebay">eBay</option>
                        <option value="etsy">Etsy</option>
                        <option value="walmart">Walmart</option>
                        <option value="other">Other</option>
                    </select>
                </div>

                <!-- Amazon-specific fields -->
                <template x-if="formData.platform === 'amazon'">
                    <div>
                        <!-- ASINs -->
                        <div class="form-group">
                            <label>
                                ASIN(s) <span class="required">*</span>
                                <div class="tooltip-container">
                                    <div class="info-icon" @click.prevent="toggleTooltip('asins', $event)" tabindex="0">i</div>
                                    <div class="tooltip" :class="{ 'show': activeTooltip === 'asins' }">
                                        <h4>What & Where</h4>
                                        <p>10-char Amazon product ID; visible on the product page URL and Seller Central.</p>
                                        <h4>Why</h4>
                                        <p>Amazon ties violations and reinstatements to ASINs.</p>
                                    </div>
                                </div>
                            </label>
                            <div x-data="{ newAsin: '' }">
                                <div style="display: flex; gap: 8px; margin-bottom: 8px;">
                                    <input type="text" 
                                           x-model="newAsin" 
                                           placeholder="Enter ASIN (10 characters)"
                                           maxlength="10"
                                           style="flex: 1;"
                                           hx-post="/legal_opinion/validate_field"
                                           hx-trigger="input delay:300ms"
                                           hx-ext="json-enc"
                                           hx-vals='{"field": "asin", "value": this.value}'
                                           @keydown.enter.prevent="if(newAsin.length === 10) { formData.asins.push(newAsin); newAsin = ''; }">
                                    <button type="button" 
                                            @click="if(newAsin.length === 10) { formData.asins.push(newAsin); newAsin = ''; }"
                                            :disabled="newAsin.length !== 10"
                                            style="background: #4CAF50; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
                                        Add
                                    </button>
                                </div>
                                <div class="validation-message" id="asin-validation"></div>
                            </div>
                            
                            <!-- ASIN List -->
                            <div x-show="formData.asins.length > 0">
                                <template x-for="(asin, index) in formData.asins" :key="index">
                                    <div class="file-item">
                                        <span class="file-name" x-text="asin"></span>
                                        <button type="button" 
                                                class="file-remove" 
                                                @click="formData.asins.splice(index, 1)">
                                            Remove
                                        </button>
                                    </div>
                                </template>
                            </div>
                        </div>


                    </div>
                </template>


                <!-- Patent Number -->
                <div class="form-group">
                    <label for="patentNumber">
                        Accusing Design Patent Registration Number <span class="required">*</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('patentNumber', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'patentNumber' }">
                                <h4>What & Where</h4>
                                <p>U.S. design patent number (e.g., USD900000S) as cited by the complainant/Amazon notice.</p>
                                <h4>Why</h4>
                                <p>Enables term/scope check and image pull. (Design term is 15 years from grant for filings on/after May 13, 2015.)</p>
                            </div>
                        </div>
                    </label>
                    <input type="text"
                           id="patentNumber"
                           x-model="formData.patentNumber"
                           hx-post="/legal_opinion/validate_field"
                           hx-trigger="change delay:500ms"
                           hx-ext="json-enc"
                           hx-vals='{"field": "patentNumber", "value": this.value}'
                           placeholder="e.g., USD1234567S"
                           required>
                    <div class="validation-message" id="patentNumber-validation"></div>
                </div>

                <!-- File Upload: Complaint Notice -->
                <div class="form-group">
                    <label>
                        Upload — Notice/Complaint from Marketplace <span class="optional">(Optional)</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('complaintFiles', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'complaintFiles' }">
                                <h4>What & Where</h4>
                                <p>The actual email or PDF/HTML export of the Performance Notification/violation page.</p>
                                <h4>Why</h4>
                                <p>Contains the policy cited, ASINs, respond-by date, and sometimes the rights-holder contact.</p>
                            </div>
                        </div>
                    </label>
                    <div class="file-upload-area"
                         @click="$refs.complaintFiles.click()"
                         @dragover.prevent
                         @drop.prevent="handleFileUpload('complaint', $event.dataTransfer.files)">
                        <input type="file"
                               x-ref="complaintFiles"
                               class="file-upload-input"
                               multiple
                               accept=".pdf,.eml,.msg,.html,.png,.jpg,.jpeg"
                               @change="handleFileUpload('complaint', $event.target.files)">
                        <div class="file-upload-text">
                            <strong>Click to upload</strong> or drag and drop<br>
                            PDF, EML/MSG, HTML, PNG/JPG (Max 25MB each)
                        </div>
                    </div>
                    <div class="file-list" x-show="files.complaint.length > 0">
                        <template x-for="file in files.complaint" :key="file.id">
                            <div class="file-item">
                                <span class="file-name" x-text="file.name"></span>
                                <span class="file-size" x-text="(file.size / 1024 / 1024).toFixed(2) + ' MB'"></span>
                                <button type="button" class="file-remove" @click="removeFile('complaint', file.id)">Remove</button>
                            </div>
                        </template>
                    </div>
                </div>

                <button type="button"
                        @click="markSectionCompleted('complaint')"
                        :disabled="!formData.storeName || !formData.platform || !formData.patentNumber || (formData.platform === 'amazon' && formData.asins.length === 0)"
                        style="background: #1E2442; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-top: 16px;"
                        :style="{ opacity: (!formData.storeName || !formData.platform || !formData.patentNumber || (formData.platform === 'amazon' && formData.asins.length === 0)) ? 0.5 : 1 }">
                    Complete Section 1
                </button>
            </div>
        </div>



        <!-- Section 2: Accused Product -->
        <div id="product" class="form-section" :class="{ 'completed': sections.product.completed }">
            <div class="section-header" @click="toggleSection('product')">
                <h3>
                    <span class="section-number">2</span>
                    Accused Product
                </h3>
                <button type="button" class="section-toggle">
                    <span x-text="sections.product.expanded ? '−' : '+'"></span>
                </button>
            </div>

            <div class="section-content" x-show="sections.product.expanded" x-transition>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 20px; font-size: 0.9em;">
                    Details about your client's product that is accused of infringement and any relevant client IP.
                </p>

                <!-- Product Description -->
                <div class="form-group">
                    <label for="productDescription">
                        Product Description <span class="required">*</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('productDescription', $event)" tabindex="0" @keydown.enter="toggleTooltip('productDescription', $event)">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'productDescription' }">
                                <h4>What & Where</h4>
                                <p>The title or description of the accused product as it appears in the marketplace listing (e.g., product title from Amazon, eBay, etc.).</p>
                                <h4>Why</h4>
                                <p>This helps identify what type of product is being accused and allows comparison with the patent to understand the scope of the alleged infringement.</p>
                            </div>
                        </div>
                    </label>
                    <textarea id="productDescription"
                              x-model="formData.productDescription"
                              placeholder="Enter the product title/description from the marketplace listing"
                              rows="3"
                              maxlength="500"
                              required></textarea>
                    <div class="validation-message" id="productDescription-validation"></div>
                </div>

                <!-- Product Images -->
                <div class="form-group">
                    <label>
                        Product Images <span class="required">*</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('productImages', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'productImages' }">
                                <h4>What & Where</h4>
                                <p>Clear photos of front, back, left, right, top, bottom; close-ups of ornamental features; and packaging.</p>
                                <h4>Why</h4>
                                <p>Ordinary-observer analysis is visual; you'll build side-by-sides to show overall impression differences.</p>
                            </div>
                        </div>
                    </label>
                    <div class="file-upload-area"
                         @click="$refs.productImages.click()"
                         @dragover.prevent
                         @drop.prevent="handleFileUpload('productImages', $event.dataTransfer.files)">
                        <input type="file"
                               x-ref="productImages"
                               class="file-upload-input"
                               multiple
                               accept=".png,.jpg,.jpeg"
                               @change="handleFileUpload('productImages', $event.target.files)">
                        <div class="file-upload-text">
                            <strong>Click to upload</strong> or drag and drop<br>
                            PNG/JPG (min 1200px shortest side, up to 20 files, Max 25MB each)<br>
                            <small style="color: rgba(255,255,255,0.6);">Include: front, back, left, right, top, bottom, packaging</small>
                        </div>
                    </div>
                    <div class="file-list" x-show="files.productImages.length > 0">
                        <template x-for="file in files.productImages" :key="file.id">
                            <div class="file-item">
                                <span class="file-name" x-text="file.name"></span>
                                <span class="file-size" x-text="(file.size / 1024 / 1024).toFixed(2) + ' MB'"></span>
                                <button type="button" class="file-remove" @click="removeFile('productImages', file.id)">Remove</button>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Commercialization Dates -->
                 <br>
                <div class="form-group">
                    <label>
                        Commercialization Dates <span class="optional">(Optional)</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('commercializationDates', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'commercializationDates' }">
                                <h4>What & Where</h4>
                                <p>Provide the date of the first public display of the product anywhere in the world, and the date of the first sale or import into the U.S.</p>
                                <h4>Why</h4>
                                <p>These dates are crucial for building a timeline of events and can be important for the legal analysis, particularly in relation to prior art and patent validity.</p>
                            </div>
                        </div>
                    </label>
                    <div class="date-group">
                        <label for="firstPublicDisplayDate">First public display anywhere</label>
                        <input type="date" id="firstPublicDisplayDate" x-model="formData.firstPublicDisplayDate" :disabled="formData.firstPublicDisplayDateUnknown">
                        <input type="checkbox" id="firstPublicDisplayDateUnknown" x-model="formData.firstPublicDisplayDateUnknown">
                        <label for="firstPublicDisplayDateUnknown">Unknown</label>
                    </div>
                    <div class="date-group">
                        <label for="firstSaleDate">First U.S. sale/import</label>
                        <input type="date" id="firstSaleDate" x-model="formData.firstSaleDate" :disabled="formData.firstSaleDateUnknown">
                        <input type="checkbox" id="firstSaleDateUnknown" x-model="formData.firstSaleDateUnknown">
                        <label for="firstSaleDateUnknown">Unknown</label>
                    </div>
                </div>

                <!-- Client Design Patents -->
                 <br>
                <div class="form-group">
                    <label>
                        Seller's Design Patent/Registration Numbers (U.S. or foreign) <span class="optional">(Optional)</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('clientPatents', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'clientPatents' }">
                                <h4>What & Where</h4>
                                <p>Any design patents/registrations covering your product; include publication/registration dates.</p>
                                <h4>Why</h4>
                                <p>If earlier than the asserted patent, they can be prior art/comparison art that narrows scope; if later, not a defense but still useful for visuals.</p>
                            </div>
                        </div>
                    </label>
                    <div x-data="{ newPatent: { number: '', country: 'US', date: '' } }">
                        <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 8px; margin-bottom: 8px;">
                            <input type="text"
                                   x-model="newPatent.number"
                                   placeholder="Patent/Registration number">
                            <select x-model="newPatent.country">
                                <option value="US">United States</option>
                                <option value="EP">European Union</option>
                                <option value="CN">China</option>
                                <option value="JP">Japan</option>
                                <option value="KR">South Korea</option>
                                <option value="OTHER">Other</option>
                            </select>
                            <input type="date" x-model="newPatent.date">
                            <button type="button"
                                    @click="if(newPatent.number) { formData.clientPatents.push({...newPatent}); newPatent = { number: '', country: 'US', date: '' }; }"
                                    :disabled="!newPatent.number"
                                    style="background: #1E2442; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
                                Add
                            </button>
                        </div>
                    </div>

                    <!-- Client Patents List -->
                    <div x-show="formData.clientPatents.length > 0">
                        <template x-for="(patent, index) in formData.clientPatents" :key="index">
                            <div class="file-item">
                                <span class="file-name" x-text="`${patent.number} (${patent.country}) - ${patent.date}`"></span>
                                <div x-if="patent.country !== 'US'">
                                    <input type="file" @change="handleFileUpload('clientPatentDocs', $event.target.files, index)">
                                </div>
                                <button type="button"
                                        class="file-remove"
                                        @click="formData.clientPatents.splice(index, 1)">
                                    Remove
                                </button>
                            </div>
                        </template>
                    </div>
                </div>

                <button type="button"
                        @click="markSectionCompleted('product')"
                        :disabled="!formData.productDescription || files.productImages.length === 0"
                        style="background: #1E2442; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-top: 16px;"
                        :style="{ opacity: (!formData.productDescription || files.productImages.length === 0) ? 0.5 : 1 }">
                    Complete Section 2
                </button>
            </div>
        </div>



        <!-- Section 3: Known Prior Art -->
        <div id="priorArt" class="form-section" :class="{ 'completed': sections.priorArt.completed }">
            <div class="section-header" @click="toggleSection('priorArt')">
                <h3>
                    <span class="section-number">3</span>
                    Known Prior Art
                </h3>
                <button type="button" class="section-toggle">
                    <span x-text="sections.priorArt.expanded ? '−' : '+'"></span>
                </button>
            </div>

            <div class="section-content" x-show="sections.priorArt.expanded" x-transition>
                <p style="color: rgba(255, 255, 255, 0.8); margin-bottom: 20px; font-size: 0.9em;">
                    Information about prior similar designs that may be relevant to the patent analysis.
                </p>

                <!-- Prior Similar Designs -->
                <div class="form-group">
                    <label>
                        Prior Similar Designs (links/files) <span class="optional">(Optional)</span>
                        <div class="tooltip-container">
                            <div class="info-icon" @click.prevent="toggleTooltip('priorArt', $event)" tabindex="0">i</div>
                            <div class="tooltip" :class="{ 'show': activeTooltip === 'priorArt' }">
                                <h4>What & Where</h4>
                                <p>Older catalogs, competitor products, earlier patents/registrations.</p>
                                <h4>Why</h4>
                                <p>Prior art informs and narrows the scope of the asserted design when applying ordinary-observer.</p>
                            </div>
                        </div>
                    </label>

                    <div x-data="{ newPriorArt: { url: '', source: '', date: '' } }">
                        <div style="display: grid; grid-template-columns: 2fr 1fr 1fr auto; gap: 8px; margin-bottom: 8px;">
                            <input type="url"
                                   x-model="newPriorArt.url"
                                   placeholder="URL to prior art">
                            <input type="text"
                                   x-model="newPriorArt.source"
                                   placeholder="Source (e.g., catalog, website)">
                            <input type="date" x-model="newPriorArt.date">
                            <button type="button"
                                    @click="if(newPriorArt.url) { formData.priorArt.push({...newPriorArt}); newPriorArt = { url: '', source: '', date: '' }; }"
                                    :disabled="!newPriorArt.url"
                                    style="background: #1E2442; color: white; border: none; padding: 8px 16px; border-radius: 4px;">
                                Add
                            </button>
                        </div>
                    </div>

                    <!-- Prior Art List -->
                    <div x-show="formData.priorArt.length > 0">
                        <template x-for="(art, index) in formData.priorArt" :key="index">
                            <div class="file-item">
                                <span class="file-name" x-text="`${art.source} - ${art.date}`"></span>
                                <button type="button"
                                        class="file-remove"
                                        @click="formData.priorArt.splice(index, 1)">
                                    Remove
                                </button>
                            </div>
                        </template>
                    </div>
                </div>

                <!-- Prior Art Files Upload -->
                <div class="form-group">
                    <label>
                        Upload Prior Art Files <span class="optional">(Optional)</span>
                    </label>
                    <div class="file-upload-area"
                         @click="$refs.priorArtFiles.click()"
                         @dragover.prevent
                         @drop.prevent="handleFileUpload('priorArtFiles', $event.dataTransfer.files)">
                        <input type="file"
                               x-ref="priorArtFiles"
                               class="file-upload-input"
                               multiple
                               accept=".pdf,.png,.jpg,.jpeg"
                               @change="handleFileUpload('priorArtFiles', $event.target.files)">
                        <div class="file-upload-text">
                            <strong>Click to upload</strong> or drag and drop<br>
                            PDF, PNG, JPG files (Max 25MB each)
                        </div>
                    </div>
                    <div class="file-list" x-show="files.priorArtFiles.length > 0">
                        <template x-for="file in files.priorArtFiles" :key="file.id">
                            <div class="file-item">
                                <span class="file-name" x-text="file.name"></span>
                                <span class="file-size" x-text="(file.size / 1024 / 1024).toFixed(2) + ' MB'"></span>
                                <button type="button" class="file-remove" @click="removeFile('priorArtFiles', file.id)">Remove</button>
                            </div>
                        </template>
                    </div>
                </div>

                <button type="button"
                        @click="markSectionCompleted('priorArt')"
                        style="background: #1E2442; color: white; border: none; padding: 10px 20px; border-radius: 6px; cursor: pointer; margin-top: 16px;">
                    Complete Section 3
                </button>
            </div>
        </div>

        <!-- Submit Button -->
        <div style="text-align: center; margin-top: 40px;">
            <button type="submit"
                    style="background: #1E2442; color: white; border: none; padding: 16px 32px; border-radius: 8px; font-size: 1.1em; cursor: pointer;">
                Submit Legal Opinion Request
            </button>
        </div>
    </form>
</div>


</body>
</html>
