"""
Legal Opinion Form Processor

Main processor for handling legal opinion form submissions.
Orchestrates the entire workflow from form data to processed submission workspace.
"""

import os
import json
import time
import uuid
from datetime import datetime
from typing import Dict, List, Any, Optional
from logdata import log_message

from LegalOpinion.submission_workspace import SubmissionWorkspaceManager
from LegalOpinion.patent_processor import PatentProcessor
from LegalOpinion.patent_utils import validate_patent_number_format


class LegalOpinionFormProcessor:
    """
    Main processor for legal opinion form submissions.
    
    Orchestrates the complete workflow:
    1. Create submission workspace
    2. Process asserted patent
    3. Process client patents
    4. Process product images
    5. Generate evidence manifest
    6. Create collected case JSON
    """
    
    def __init__(self, base_workspace_path: Optional[str] = None):
        """
        Initialize the form processor.
        
        Args:
            base_workspace_path: Base path for workspaces (optional)
        """
        self.workspace_manager = SubmissionWorkspaceManager(base_workspace_path)
        self.current_submission_id = None
        self.current_workspace_paths = None
        self.patent_processor = None
    
    def generate_submission_id(self) -> str:
        """
        Generate a unique submission ID.
        
        Returns:
            Submission ID in format LO-{8_char_hex}
        """
        # Generate a short UUID-based ID
        short_uuid = str(uuid.uuid4()).replace('-', '').upper()[:8]
        return f"LO-{short_uuid}"
    
    def validate_form_data(self, form_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate form data and return validation results.
        
        Args:
            form_data: Form data dictionary
            
        Returns:
            Validation results dictionary
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        # Required fields validation
        required_fields = ['storeName', 'platform', 'patentNumber', 'productDescription']
        for field in required_fields:
            if not form_data.get(field):
                validation_result["errors"].append(f"Required field missing: {field}")
                validation_result["valid"] = False
        
        # Patent number validation
        patent_number = form_data.get('patentNumber', '')
        if patent_number:
            patent_validation = validate_patent_number_format(patent_number)
            if not patent_validation["valid"]:
                validation_result["errors"].append(f"Invalid patent number: {patent_validation['message']}")
                validation_result["valid"] = False
        
        # Client patents validation
        client_patents = form_data.get('clientPatents', [])
        for i, patent in enumerate(client_patents):
            if not patent.get('patentNumber'):
                validation_result["warnings"].append(f"Client patent {i+1} missing patent number")
            if not patent.get('country'):
                validation_result["warnings"].append(f"Client patent {i+1} missing country")
        
        return validation_result
    
    async def process_submission(self, form_data: Dict[str, Any], 
                               uploaded_files: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Process a complete legal opinion submission.
        
        Args:
            form_data: Form data dictionary
            uploaded_files: Dictionary of uploaded files (optional)
            
        Returns:
            Processing results dictionary
        """
        start_time = time.time()
        
        # Generate submission ID
        submission_id = self.generate_submission_id()
        self.current_submission_id = submission_id
        
        result = {
            "success": False,
            "submission_id": submission_id,
            "processing_time": 0,
            "validation": {},
            "workspace_created": False,
            "asserted_patent": {},
            "client_patents": [],
            "product_images": {},
            "prior_art": {},
            "curver_analysis": {},
            "evidence_manifest": {},
            "errors": [],
            "warnings": []
        }
        
        try:
            log_message(f"Starting legal opinion processing for submission: {submission_id}", level='INFO')
            
            # Step 1: Validate form data
            validation_result = self.validate_form_data(form_data)
            result["validation"] = validation_result
            
            if not validation_result["valid"]:
                result["errors"].extend(validation_result["errors"])
                log_message(f"Form validation failed for {submission_id}: {validation_result['errors']}", level='ERROR')
                return result
            
            if validation_result["warnings"]:
                result["warnings"].extend(validation_result["warnings"])
            
            # Step 2: Create submission workspace
            try:
                workspace_paths = self.workspace_manager.create_submission_workspace(submission_id)
                self.current_workspace_paths = workspace_paths
                result["workspace_created"] = True
                
                # Log to workspace
                self.workspace_manager.log_to_workspace(
                    submission_id, 
                    f"Started processing legal opinion submission", 
                    level='INFO'
                )
                
            except Exception as e:
                error_msg = f"Failed to create workspace: {e}"
                result["errors"].append(error_msg)
                log_message(f"Workspace creation failed for {submission_id}: {e}", level='ERROR')
                return result
            
            # Step 3: Initialize patent processor
            self.patent_processor = PatentProcessor(submission_id, workspace_paths)
            
            # Step 4: Process asserted patent
            try:
                patent_number = form_data.get('patentNumber', '')
                asserted_result = await self.patent_processor.process_asserted_patent(patent_number)
                result["asserted_patent"] = asserted_result
                
                if not asserted_result["success"]:
                    result["warnings"].append(f"Asserted patent processing had issues: {asserted_result.get('error', 'Unknown error')}")
                
                self.workspace_manager.log_to_workspace(
                    submission_id,
                    f"Processed asserted patent: {patent_number} -> {asserted_result.get('normalized_reg_no', 'N/A')}",
                    level='INFO'
                )
                
            except Exception as e:
                error_msg = f"Error processing asserted patent: {e}"
                result["errors"].append(error_msg)
                log_message(f"Asserted patent processing failed for {submission_id}: {e}", level='ERROR')
            
            # Step 5: Process client patents
            try:
                client_patents = form_data.get('clientPatents', [])
                if client_patents:
                    # Pass asserted patent data for date comparison
                    asserted_patent_data = result.get("asserted_patent", {}).get("patent_data")
                    client_results = await self.patent_processor.process_client_patents(
                        client_patents, uploaded_files, asserted_patent_data
                    )
                    result["client_patents"] = client_results
                    
                    successful_count = sum(1 for r in client_results if r["success"])
                    self.workspace_manager.log_to_workspace(
                        submission_id,
                        f"Processed {successful_count}/{len(client_results)} client patents",
                        level='INFO'
                    )
                
            except Exception as e:
                error_msg = f"Error processing client patents: {e}"
                result["errors"].append(error_msg)
                log_message(f"Client patents processing failed for {submission_id}: {e}", level='ERROR')
            
            # Step 6: Process product images
            try:
                if uploaded_files:
                    images_result = await self.patent_processor.process_product_images(uploaded_files)
                    result["product_images"] = images_result

                    if images_result["success"]:
                        self.workspace_manager.log_to_workspace(
                            submission_id,
                            f"Processed {len(images_result['saved_files'])} product images",
                            level='INFO'
                        )

            except Exception as e:
                error_msg = f"Error processing product images: {e}"
                result["errors"].append(error_msg)
                log_message(f"Product images processing failed for {submission_id}: {e}", level='ERROR')

            # Step 7: Process prior art
            try:
                prior_art_result = await self.patent_processor.process_prior_art(form_data, uploaded_files)
                result["prior_art"] = prior_art_result

                if prior_art_result["success"]:
                    self.workspace_manager.log_to_workspace(
                        submission_id,
                        f"Processed prior art with {prior_art_result['evidence_entries']} evidence entries",
                        level='INFO'
                    )

            except Exception as e:
                error_msg = f"Error processing prior art: {e}"
                result["errors"].append(error_msg)
                log_message(f"Prior art processing failed for {submission_id}: {e}", level='ERROR')
            
            # Step 8: Finalize processing (save manifests, create collected case JSON)
            try:
                finalization_success = await self.patent_processor.finalize_processing(
                    form_data, result
                )
                
                if finalization_success:
                    result["evidence_manifest"] = {
                        "success": True,
                        "manifest_path": str(self.patent_processor.evidence_manager.evidence_manifest_path),
                        "collected_case_path": str(self.patent_processor.evidence_manager.collected_case_path)
                    }
                    
                    self.workspace_manager.log_to_workspace(
                        submission_id,
                        "Finalized processing - manifests and collected case JSON created",
                        level='INFO'
                    )
                else:
                    result["warnings"].append("Failed to finalize processing manifests")
                
            except Exception as e:
                error_msg = f"Error finalizing processing: {e}"
                result["errors"].append(error_msg)
                log_message(f"Finalization failed for {submission_id}: {e}", level='ERROR')
            
            # Step 9: Determine overall success
            # Success if workspace was created and no critical errors occurred
            result["success"] = (
                result["workspace_created"] and 
                len(result["errors"]) == 0
            )
            
            # Calculate processing time
            result["processing_time"] = time.time() - start_time
            
            # Final logging
            status = "SUCCESS" if result["success"] else "FAILED"
            self.workspace_manager.log_to_workspace(
                submission_id,
                f"Processing completed: {status} in {result['processing_time']:.2f} seconds",
                level='INFO' if result["success"] else 'ERROR'
            )
            
            log_message(
                f"Legal opinion processing {status} for {submission_id} in {result['processing_time']:.2f}s",
                level='INFO' if result["success"] else 'ERROR'
            )
            
        except Exception as e:
            result["errors"].append(f"Unexpected error during processing: {e}")
            result["success"] = False
            log_message(f"Unexpected error processing {submission_id}: {e}", level='ERROR')
        
        finally:
            # Cleanup resources
            if self.patent_processor:
                try:
                    await self.patent_processor.cleanup()
                except Exception as e:
                    log_message(f"Error during cleanup for {submission_id}: {e}", level='WARNING')
        
        return result
    
    def get_submission_status(self, submission_id: str) -> Dict[str, Any]:
        """
        Get the status of a submission.
        
        Args:
            submission_id: Submission ID to check
            
        Returns:
            Status information dictionary
        """
        try:
            if not self.workspace_manager.workspace_exists(submission_id):
                return {
                    "exists": False,
                    "error": "Submission not found"
                }
            
            workspace_path = self.workspace_manager.get_workspace_path(submission_id)
            
            # Check for collected case JSON
            manifests_dir = workspace_path / "manifests"
            collected_case_path = manifests_dir / "collected_case.json"
            evidence_manifest_path = manifests_dir / "evidence_manifest.json"
            
            status = {
                "exists": True,
                "submission_id": submission_id,
                "workspace_path": str(workspace_path),
                "collected_case_exists": collected_case_path.exists(),
                "evidence_manifest_exists": evidence_manifest_path.exists(),
                "created_at": None,
                "processing_complete": False
            }
            
            # Try to load collected case for more details
            if collected_case_path.exists():
                try:
                    with open(collected_case_path, 'r', encoding='utf-8') as f:
                        collected_case = json.load(f)
                    
                    status["created_at"] = collected_case.get("created_at")
                    status["processing_complete"] = True
                    status["form_data_summary"] = {
                        "store_name": collected_case.get("form_data", {}).get("storeName"),
                        "platform": collected_case.get("form_data", {}).get("platform"),
                        "patent_number": collected_case.get("form_data", {}).get("patentNumber")
                    }
                    
                except Exception as e:
                    log_message(f"Error loading collected case for {submission_id}: {e}", level='WARNING')
            
            return status
            
        except Exception as e:
            log_message(f"Error getting submission status for {submission_id}: {e}", level='ERROR')
            return {
                "exists": False,
                "error": f"Error checking status: {e}"
            }
