import sys, os, torch, gc, grpc, time, schedule, threading, re, unicodedata, threading, pickle, gzip, zstandard
from datetime import datetime
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))
import numpy as np
from concurrent import futures
from Protos import embedding_pb2, embedding_pb2_grpc
from IP.Patents_Bulk.patent_image_spliter import get_paddle_ocr
from Check.RAG.siglip_model import SiglipModel
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from Common.Constants import local_ip_folder
from Check.api_studio.grpc_auth import validate_bearer_token
import pyvips

norm = lambda s: re.sub(r'\s+', ' ', unicodedata.normalize('NFKC', s).lower()).strip()

class EmbeddingService(embedding_pb2_grpc.EmbeddingServiceServicer):
    def __init__(self):
        """
        Loads the SigLIP and PaddleOCR models.
        """
        print("Loading embedding models...")
        self.model_siglip = self.load_siglip_model()
        print("Loading PaddleOCR model...")
        self.ocr = get_paddle_ocr()
        print("Loading trademark automaton...")
        self.tm_auto, self.tm_meta = self.load_tm_automaton()
        print("Models loaded successfully.")
        
        # Start a background thread to check for automaton updates
        schedule.every(6).hours.do(self._check_and_update_automaton)
        threading.Thread(target=self._run_scheduler, daemon=True).start()
        
        self.allocated = 0
        self.reserved = 0

    def load_siglip_model(self):
        """Load the single SigLIP model used for all IP types."""
        print(f"\nLoading SigLIP model google/siglip2-large-patch16-512")
        siglip_config = {
            "vector_size": 1024,
            "model_name_or_path": "google/siglip2-large-patch16-512"
        }
        model_siglip = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
        model_siglip.load()
        print("SigLIP model loaded successfully.")
        return model_siglip
    
    def load_tm_automaton(self):
        table_name = "trademarks_precomputed_marks"
        local_path = os.path.join(local_ip_folder, "Trademarks")
        os.makedirs(local_path, exist_ok=True)
        automaton_path = os.path.join(local_path, "tm_automaton.pkl")

        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(f"SELECT built_at FROM {table_name} WHERE id = 1")
        db_timestamp_result = cur.fetchone()
        cur.close()
        conn.close()

        if db_timestamp_result is None:
            raise Exception(f"No automaton found in table {table_name}.")
        db_timestamp = db_timestamp_result[0]

        if os.path.exists(automaton_path):
            local_timestamp = datetime.fromtimestamp(os.path.getmtime(automaton_path), tz=db_timestamp.tzinfo)
            if local_timestamp >= db_timestamp:
                print("Loading trademark automaton from local file...")
                with open(automaton_path, 'rb') as f:
                    A, meta = pickle.load(f)
                print("Loaded from local file successfully.")
                return A, meta

        return self._update_automaton()

    def _update_automaton(self):
        table_name = "trademarks_precomputed_marks"
        local_path = os.path.join(local_ip_folder, "Trademarks")
        automaton_path = os.path.join(local_path, "tm_automaton.pkl")

        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(f"SELECT built_at, automaton_blob FROM {table_name} WHERE id = 1")
        result = cur.fetchone()
        if result is None:
            raise Exception(f"No automaton found in table {table_name}.")
        db_timestamp, oid = result

        print(f"Downloading trademark automaton from {table_name}...")
        start_time = time.time()
        lo = conn.lobject(oid, 'rb')
        
        lo.seek(0, 2)
        total_size = lo.tell()
        lo.seek(0, 0)

        chunk_size = 1024 * 1024
        data = bytearray()
        bytes_read = 0
        if total_size > 0:
            while bytes_read < total_size:
                chunk = lo.read(min(chunk_size, total_size - bytes_read))
                if not chunk: break
                data.extend(chunk)
                bytes_read += len(chunk)
                progress = (bytes_read / total_size) * 100
                sys.stdout.write(f"\rDownloading automaton: {bytes_read / (1024*1024):.2f}MB / {total_size / (1024*1024):.2f}MB ({progress:.2f}%)")
                sys.stdout.flush()
            sys.stdout.write('\n')

        gz_blob = bytes(data)
        lo.close()
        print(f"Time to fetch automaton: {time.time() - start_time:.2f}s. Decompressing...")

        start_time = time.time()
        try:
            decompressed_blob = zstandard.decompress(gz_blob)
            print("Decompressed using zstandard.")
        except Exception:
            print("Zstandard failed, falling back to gzip.")
            decompressed_blob = gzip.decompress(gz_blob)
        
        A, meta = pickle.loads(decompressed_blob)
        print(f"Time to decompress: {time.time() - start_time:.2f}s")
        
        with open(automaton_path, 'wb') as f:
            pickle.dump((A, meta), f)
        
        os.utime(automaton_path, (db_timestamp.timestamp(), db_timestamp.timestamp()))

        cur.close()
        conn.close()
        return A, meta

    def _check_and_update_automaton(self):
        print("Checking for trademark automaton updates...")
        table_name = "trademarks_precomputed_marks"
        local_path = os.path.join(local_ip_folder, "Trademarks")
        automaton_path = os.path.join(local_path, "tm_automaton.pkl")

        conn = get_db_connection()
        cur = conn.cursor()
        cur.execute(f"SELECT built_at FROM {table_name} WHERE id = 1")
        db_timestamp_result = cur.fetchone()
        if db_timestamp_result is None:
            return
        db_timestamp = db_timestamp_result[0]
        cur.close()
        conn.close()

        if os.path.exists(automaton_path):
            local_timestamp = datetime.fromtimestamp(os.path.getmtime(automaton_path), tz=db_timestamp.tzinfo)
            if db_timestamp > local_timestamp:
                print("Database has a newer automaton. Updating...")
                self.tm_auto, self.tm_meta = self._update_automaton()
        else:
            # Should have been loaded on startup, but as a fallback
            self.tm_auto, self.tm_meta = self._update_automaton()

    def _run_scheduler(self):
        while True:
            schedule.run_pending()
            time.sleep(60)
            
    def log_vram(self, tag):
        try:
            import torch
            alloc = torch.cuda.memory_allocated()
            reserv = torch.cuda.memory_reserved()
            if int(alloc) != int(self.allocated) or int(reserv) != int(self.reserved):
                self.allocated = alloc
                self.reserved = reserv
                print(f"[VRAM] {tag}: allocated={alloc/1e6:.1f}MB reserved={reserv/1e6:.1f}MB")
        except Exception:
            pass


    @validate_bearer_token
    def GetEmbeddings(self, request, context):
        # Check the type of the first input to determine the data type for the batch
        if not request.inputs:
            return embedding_pb2.GetEmbeddingsResponse()

        first_input = request.inputs[0]
        if first_input.HasField("image_data"):
            data_type = "image"
            data_list = [inp.image_data for inp in request.inputs]
        elif first_input.HasField("text_data"):
            data_type = "text"
            data_list = [inp.text_data for inp in request.inputs]
        else:
            # Handle empty input case if necessary
            return embedding_pb2.GetEmbeddingsResponse()

        # Get embeddings (this function preserves order)
        if not data_list:
            return embedding_pb2.GetEmbeddingsResponse()
        embeddings = self.get_siglip_embeddings(data_list, data_type=data_type)
        
        # Create the response
        response = embedding_pb2.GetEmbeddingsResponse()
        for emb in embeddings.tolist():
            if len(emb) != 1024:
                raise ValueError(f"Invalid embedding size: {len(emb)}")
            embedding_proto = response.embeddings.add()
            embedding_proto.embedding.extend(emb)
        return response

    @validate_bearer_token
    def GetImageSplitWithEmbeddings(self, request, context):
        response = embedding_pb2.GetImageSplitWithEmbeddingsResponse()
        ocr_time = 0
        embedding_time = 0
        processing_time = 0
        
        for image_info in request.images:
            image_bytes = image_info.image_data
            original_filename = image_info.original_filename
            
            image_splits, new_ocr_time, new_embedding_time = self._get_image_splits_with_embeddings(image_bytes, original_filename)
            ocr_time += new_ocr_time
            embedding_time += new_embedding_time
            
            result = response.results.add()
            result.original_filename = original_filename
            
            start_time = time.time()
            if image_splits:
                for i, (split_img, embedding, filename) in enumerate(zip(image_splits['split_images'], image_splits['embeddings'], image_splits['filenames'])):
                    image_split = result.image_splits.add()
                    
                    split_img = np.repeat(split_img[..., None], 3, axis=2)
                    h, w = split_img.shape[:2]

                    # Determine if miniswhite should be used based on the background color
                    # A mostly white background (high mean pixel value) should use miniswhite
                    is_mostly_white = np.mean(split_img) > 200
                    miniswhite = is_mostly_white
                    
                    # Wrap NumPy memory as a vips image (1 band, uchar)
                    vimg = pyvips.Image.new_from_memory(np.ascontiguousarray(split_img).data, w, h, 1, 'uchar')
                    
                    # Save as TIFF with CCITT Group 4 compression
                    buf = vimg.tiffsave_buffer(compression='ccittfax4', bitdepth=1, miniswhite=miniswhite)
                    
                    image_split.image_data = buf
                    
                    image_split.filename = filename
                    image_split.embedding.extend(embedding) # Embedding is already a list of floats, no need to convert to protobuf format
                    
                    # 🔒 free PIL image memory asap
                    try:
                        split_img.close()
                        buf = None
                        del vimg
                    except Exception:
                        pass
                
                # If the image was not split, associate the original rectangles with the new filename.
                if len(image_splits['split_images']) == 1:
                    new_filename = image_splits['filenames'][0]
                    rect_list_proto = embedding_pb2.RectangleList()
                    for rect in image_splits['rects']:
                        rect_proto = rect_list_proto.rects.add()
                        rect_proto.points.extend(rect.flatten().tolist())
                    result.rects_map[new_filename].CopyFrom(rect_list_proto)
                    
                # help GC between large batches
                del image_splits
            processing_time += time.time() - start_time
                
        gc.collect()
        print(f"OCR time: {ocr_time:.2f}s, Embedding time: {embedding_time:.2f}s, Processing time: {processing_time:.2f}s")
        return response

    def get_siglip_embeddings(self, data_list, data_type="image", batch_size=1):
        """
        Get SigLIP embeddings for images or text.

        Args:
            data_list: A single image path/text string or a list of image paths/text strings.
            data_type: "image" or "text"
            batch_size: Batch size for processing

        Returns:
            numpy array of embeddings
        """
        if isinstance(data_list, str):
            data_list = [data_list]

        if not self.model_siglip:
            self.model_siglip = self.load_siglip_model()

        all_embeddings_list = []
        
        # Batch processing
        current_batch_size = batch_size

        while current_batch_size >= 1:
            all_embeddings_list = [] # Reset for each batch size attempt
            try:
                for i in range(0, len(data_list), current_batch_size):
                    batch = data_list[i:i+current_batch_size]
                    batch_embeddings = self.model_siglip.compute_features(batch, data_type=data_type)
                    all_embeddings_list.append(batch_embeddings)
                break # Success
            except RuntimeError as e:
                if 'out of memory' in str(e).lower():
                    print(f"OOM at batch {current_batch_size}, reducing…")
                    current_batch_size = current_batch_size // 2
                    try:
                        torch.cuda.empty_cache()
                    except Exception:
                        pass
                    gc.collect()
                else:
                    raise e

        if not all_embeddings_list:
            raise RuntimeError("Failed to compute embeddings even with batch size 1.")

        out = np.concatenate(all_embeddings_list, axis=0)
        del all_embeddings_list
        return out


    def test_speed_siglip(self):
        # All 50 pics
        # CPU laptop: 1=144 sec, 2=130sec
        # GPU laptop: 1=8sec, 2=7.4-9.9sec, 4=6.7-9.4sec, 8=50sec
        # GPU 3070:  1 =5.4sec, 2=5.4, 4 = 5.4sec
        # CPU Server: 1=104sec, 2=112sec
        
        from Common.Constants import local_ip_tro_folder

        # Conclusion: GPU is way way fast. This model is same speed as Jina clipv2
        folder_path = os.path.join(local_ip_tro_folder, "copyrights", "Production")
        image_paths = [os.path.join(folder_path, f) for f in os.listdir(folder_path) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.gif', '.bmp', '.webp'))]
        image_paths = image_paths[:50]

        embeddings = self.get_siglip_embeddings(image_paths[:1]) # to get cuDNN loaded

        start_time = time.time()
        embeddings = self.get_siglip_embeddings(image_paths, batch_size=2)
        end_time = time.time()
        print(f"Total time taken batch size 4: {end_time - start_time:.1f} seconds")


    def _get_image_splits_with_embeddings(self, image_bytes, original_filename):
        """Splits an image and generates embeddings for each split."""
        try:
            start_time = time.time()
            from IP.Patents_Bulk.patent_image_spliter import process_one
            from pathlib import Path
            import tempfile
            import re

            regex = re.compile(r'^fig', re.I)
            
            success, code, split_images, rects = process_one(
                path_or_bytes=image_bytes,
                out_root=None,
                debug_root=Path(tempfile.gettempdir()),
                ocr=self.ocr,
                regex=regex,
                original_filename=original_filename,
                return_bytes=True
            )
            ocr_time = time.time() - start_time
            
            start_time = time.time()
            results = {}
            if success:
                original_stem = Path(original_filename).stem
                # Pass PIL images directly to the embedding model
                # self.log_vram("AfterOCR")
                embeddings = self.get_siglip_embeddings(split_images, data_type="image")
                # self.log_vram("AfterSiglip")
                
                filenames = []
                for i, _ in enumerate(split_images):
                    if len(split_images) == 1:
                        new_filename = f"{original_stem}.tiff"
                    else:
                        new_filename = f"{original_stem}_{i + 1}.tiff"
                    filenames.append(new_filename)
                
                results = {
                    'split_images': split_images,
                    'embeddings': embeddings.tolist(),
                    'rects': rects,
                    'filenames': filenames,
                }
            else:
                if code == "invalid_image":
                    print(f"Processing failed for {original_filename}: The image has invalid dimensions (e.g., zero width or height).")
                else:
                    print(f"Processing failed for {original_filename} with code: {code}")

            embedding_time = time.time() - start_time
            # print(f"OCR time: {ocr_time:.2f}s, Embedding time: {embedding_time:.2f}s")
            return results, ocr_time, embedding_time
        except Exception as e:
            print(f"Error splitting image {original_filename} with embeddings: {e}")
            raise


    def GetAllMatches(self, request, context):
        text = norm(request.text)
        matches = []
        is_perfect = False
        for end_idx, tm_ids in self.tm_auto.iter(text):
            for tm_id in tm_ids:
                matched_word_normalized = self.tm_meta[tm_id][0]
                start_idx = end_idx - len(matched_word_normalized) + 1
                is_start_boundary = (start_idx == 0) or (not text[start_idx - 1].isalnum())
                is_end_boundary = (end_idx == len(text) - 1) or (not text[end_idx + 1].isalnum())
                if is_start_boundary and is_end_boundary:
                    meta = self.tm_meta[tm_id]
                    
                    # Create TrademarkMetadata
                    trademark_meta_args = {
                        'mark_text': meta[0] or "",
                        'reg_no': meta[2] or "",
                        'ser_no': meta[3] or "",
                        'applicant_name': meta[4] or "",
                        'int_cls': meta[5] if isinstance(meta[5], list) else [],
                        'goods_services_text_daily': meta[6] or ""
                    }
                    if meta[1] is not None:
                        trademark_meta_args['plaintiff_id'] = int(meta[1])
                    
                    trademark_meta = embedding_pb2.TrademarkMetadata(**trademark_meta_args)

                    matches.append(embedding_pb2.Match(
                        start=int(start_idx),
                        end=int(end_idx),
                        metadata=trademark_meta
                    ))

                    # Check for a perfect match
                    if start_idx == 0 and end_idx == len(text) - 1:
                        is_perfect = True
                        
        return embedding_pb2.GetAllMatchesResponse(matches=matches, is_perfect=is_perfect)

def serve():
    server = grpc.server(
        futures.ThreadPoolExecutor(max_workers=1),
        options=[
            ('grpc.max_send_message_length', 100 * 1024 * 1024),
            ('grpc.max_receive_message_length', 100 * 1024 * 1024),
        ]
    )
    embedding_pb2_grpc.add_EmbeddingServiceServicer_to_server(EmbeddingService(), server)
    server.add_insecure_port('[::]:5001')
    server.start()
    print("Embedding gRPC server started on port 5001.")
    server.wait_for_termination()

if __name__ == '__main__':
    serve()