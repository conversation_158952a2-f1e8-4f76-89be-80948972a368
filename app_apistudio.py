import os
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from arq import create_pool
import httpx
from Check.ARQ.worker import WorkerSettings

# Import the new APIRouter modules
from Check.api_studio.check_routes import check_bp as check_router
from Check.api_studio.history_routes import history_router
from Check.api_studio.admin_routes import admin_router
from Check.api_studio.embedding_routes import embedding_router

# Import Legal Opinion routes
from LegalOpinion.legal_opinion_routes import legal_opinion_router

from prometheus_fastapi_instrumentator import Instrumentator
from prometheus_client import multiprocess, CollectorRegistry
from Check.RAG.qdrant_search import init_aiohttp_session, close_aiohttp_session
from Check.Do_Check_Download import init_download_session, close_download_session

# ❌⚠️📥🔥✅☑️✔️💯💧⛏🔨🗝🔑 🔒💡⛔🚫❗





@asynccontextmanager
async def lifespan(app: FastAPI):
   # Startup
   await init_aiohttp_session()
   await init_download_session()
   if os.getenv("DEBUG"):
       # In debug mode, run jobs inline without Redis
       arq_pool = await create_pool(job_serializer=None)
   else:
       arq_pool = await create_pool(WorkerSettings.redis_settings)
   app.state.arq_pool = arq_pool
   yield
   # Shutdown
   await close_aiohttp_session()
   await close_download_session()
   if hasattr(app.state, "arq_pool") and app.state.arq_pool:
       await app.state.arq_pool.close()

# Create FastAPI app
def create_app():
    app = FastAPI(lifespan=lifespan)
    # Configure Prometheus Instrumentator for multi-process mode
    if os.environ.get("DEBUG",0) != "1":
        registry = CollectorRegistry()
        os.environ.setdefault("PROMETHEUS_MULTIPROC_DIR", os.path.join(os.getcwd(), "prometheus_metrics"))
        print(f"Prometheus metrics directory: {os.environ['PROMETHEUS_MULTIPROC_DIR']}")
        multiprocess.MultiProcessCollector(registry)
        Instrumentator(registry=registry).instrument(app).expose(app)
        
    app.mount("/static", StaticFiles(directory="static"), name="static")
    templates = Jinja2Templates(directory="templates")
    
    templates.env.globals["url_path_for"] = app.url_path_for

    # Register Routers
    app.include_router(check_router, prefix="")
    app.include_router(history_router, prefix="/history")
    app.include_router(admin_router, prefix="/admin")
    app.include_router(embedding_router, prefix="")
    app.include_router(legal_opinion_router, prefix="/legal_opinion")

    @app.get("/health")
    async def health_check():
        return {"status": "ok"}

    # UI Routes
    @app.get('/api_studio_lite', response_class=HTMLResponse)
    async def api_studio_lite_route(request: Request):
        return templates.TemplateResponse("api_studio_lite.html", {"request": request})

    @app.get('/api_studio', response_class=HTMLResponse)
    async def api_studio_route(request: Request):
        return templates.TemplateResponse("api_studio.html", {"request": request})

    @app.get('/api_studio_reverse_check', response_class=HTMLResponse)
    async def api_studio_reverse_check_route(request: Request):
        return templates.TemplateResponse("api_studio_reverse_check.html", {"request": request})

    @app.get('/documentation/{lang}', response_class=HTMLResponse)
    async def documentation_route(request: Request, lang: str):
        """
        Serves the documentation in the specified language.
        Defaults to English if the language is not supported.
        """
        supported_langs = ['en', 'zh']
        if lang not in supported_langs:
            lang = 'en'
        
        template_name = f'documentation_{lang}.html'
        return templates.TemplateResponse(template_name, {"request": request})

    @app.get('/check_history', response_class=HTMLResponse)
    async def check_history_route(request: Request):
        return templates.TemplateResponse("check_history.html", {"request": request})

    @app.get('/legal_opinion', response_class=HTMLResponse)
    async def legal_opinion_route(request: Request):
        return templates.TemplateResponse("legal_opinion.html", {"request": request})











    @app.post('/reverse_check_status')
    async def reverse_check_status_route(request: Request):
        """Route to relay reverse check status requests to the Qdrant API"""
        qdrant_api_url = os.getenv("QDRANT_API_URL")
        if not qdrant_api_url:
            return JSONResponse(content={"error": "QDRANT_API_URL environment variable not set"}, status_code=500)

        try:
            data = await request.json()
            # Pass through the headers from the original request.
            # We filter out host, as it's not a good practice to pass it on.
            headers = {
                name: value
                for name, value in request.headers.items()
                if name.lower() != "host"
            }
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    f"{qdrant_api_url}/reverse_check_status",
                    json=data,
                    headers=headers,
                    timeout=30
                )
            return JSONResponse(content=response.json(), status_code=response.status_code)

        except httpx.RequestError as e:
            return JSONResponse(content={"error": f"Failed to connect to Qdrant API: {str(e)}"}, status_code=500)
        except Exception as e:
            return JSONResponse(content={"error": f"Server error: {str(e)}"}, status_code=500)
        
    return app

# Check API project
os.environ["LANGFUSE_SECRET_KEY"] = "******************************************"
os.environ["LANGFUSE_PUBLIC_KEY"] = "pk-lf-a81ed7b3-cbf2-494a-a6ff-c00b21778891"

# app = create_app()

if __name__ == "__main__":
    # For debug purpose: run wsl for redis, desactivate the app=create_app() in the code + pip install uvicorn
    os.environ["DEBUG"] = "1"  # Set to "1" to enable debug mode
    app = create_app() # Create the app with the new environment variable  (deactivate the other one in the main code)
    import uvicorn, asyncio
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())
    uvicorn.run(app, host="0.0.0.0", port=5089)