"""
Evidence Manager

Handles SHA256 hashing of all files and maintains evidence manifest
for tracking file provenance in legal opinion processing.
"""

import os
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from logdata import log_message


class EvidenceManager:
    """
    Manages evidence tracking and file hashing for legal opinion submissions.
    
    Maintains an evidence manifest that tracks all files with their hashes,
    provenance, and metadata for downstream pipeline processing.
    """
    
    def __init__(self, submission_id: str, manifests_dir: str):
        """
        Initialize the evidence manager.
        
        Args:
            submission_id: Unique submission identifier
            manifests_dir: Directory where manifest files are stored
        """
        self.submission_id = submission_id
        self.manifests_dir = Path(manifests_dir)
        self.evidence_manifest_path = self.manifests_dir / "evidence_manifest.json"
        self.collected_case_path = self.manifests_dir / "collected_case.json"
        
        # Ensure manifests directory exists
        self.manifests_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize or load existing manifest
        self.evidence_manifest = self._load_or_create_manifest()
    
    def _load_or_create_manifest(self) -> Dict[str, Any]:
        """
        Load existing evidence manifest or create a new one.
        
        Returns:
            Evidence manifest dictionary
        """
        if self.evidence_manifest_path.exists():
            try:
                with open(self.evidence_manifest_path, 'r', encoding='utf-8') as f:
                    manifest = json.load(f)
                log_message(f"Loaded existing evidence manifest for {self.submission_id}", level='INFO')
                return manifest
            except (json.JSONDecodeError, IOError) as e:
                log_message(f"Error loading evidence manifest: {e}, creating new one", level='WARNING')
        
        # Create new manifest
        manifest = {
            "submission_id": self.submission_id,
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "version": "1.0.0",
            "files": {},
            "categories": {
                "asserted_patent": [],
                "client_patents": [],
                "product_images": [],
                "prior_art": [],
                "complaint_docs": [],
                "other": []
            },
            "statistics": {
                "total_files": 0,
                "total_size_bytes": 0,
                "files_by_category": {}
            }
        }
        
        log_message(f"Created new evidence manifest for {self.submission_id}", level='INFO')
        return manifest
    
    def compute_file_hash(self, file_path: Union[str, Path]) -> str:
        """
        Compute SHA256 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA256 hash as hex string
        """
        sha256_hash = hashlib.sha256()
        file_path = Path(file_path)
        
        try:
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files efficiently
                for chunk in iter(lambda: f.read(8192), b""):
                    sha256_hash.update(chunk)
            
            hash_value = sha256_hash.hexdigest()
            log_message(f"Computed hash for {file_path.name}: {hash_value[:16]}...", level='DEBUG')
            return hash_value
            
        except IOError as e:
            log_message(f"Error computing hash for {file_path}: {e}", level='ERROR')
            return ""
    
    def add_file_to_manifest(self, file_path: Union[str, Path], category: str, 
                           provenance: str, metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Add a file to the evidence manifest with hash and metadata.
        
        Args:
            file_path: Path to the file
            category: File category (asserted_patent, client_patents, etc.)
            provenance: Source/origin of the file (e.g., "USPTO_API", "client_upload", "USPTO_Grants")
            metadata: Additional metadata about the file
            
        Returns:
            True if successfully added, False otherwise
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            log_message(f"File not found: {file_path}", level='ERROR')
            return False
        
        try:
            # Compute file hash
            file_hash = self.compute_file_hash(file_path)
            if not file_hash:
                return False
            
            # Get file stats
            file_stats = file_path.stat()
            
            # Create file entry
            file_entry = {
                "path": str(file_path),
                "filename": file_path.name,
                "category": category,
                "provenance": provenance,
                "sha256": file_hash,
                "size_bytes": file_stats.st_size,
                "created_at": datetime.fromtimestamp(file_stats.st_ctime).isoformat(),
                "modified_at": datetime.fromtimestamp(file_stats.st_mtime).isoformat(),
                "added_to_manifest": datetime.now().isoformat(),
                "metadata": metadata or {}
            }
            
            # Add to manifest
            file_key = f"{category}_{file_path.name}_{file_hash[:8]}"
            self.evidence_manifest["files"][file_key] = file_entry
            
            # Add to category list
            if category not in self.evidence_manifest["categories"]:
                self.evidence_manifest["categories"][category] = []
            self.evidence_manifest["categories"][category].append(file_key)
            
            # Update statistics
            self._update_statistics()
            
            # Update last modified timestamp
            self.evidence_manifest["last_updated"] = datetime.now().isoformat()
            
            log_message(f"Added file to manifest: {file_path.name} ({category})", level='INFO')
            return True
            
        except Exception as e:
            log_message(f"Error adding file to manifest {file_path}: {e}", level='ERROR')
            return False
    
    def add_directory_to_manifest(self, directory_path: Union[str, Path], category: str, 
                                provenance: str, recursive: bool = True) -> int:
        """
        Add all files in a directory to the evidence manifest.
        
        Args:
            directory_path: Path to the directory
            category: File category for all files
            provenance: Source/origin of the files
            recursive: Whether to include subdirectories
            
        Returns:
            Number of files successfully added
        """
        directory_path = Path(directory_path)
        
        if not directory_path.exists() or not directory_path.is_dir():
            log_message(f"Directory not found: {directory_path}", level='ERROR')
            return 0
        
        added_count = 0
        
        try:
            # Get all files in directory
            if recursive:
                files = directory_path.rglob('*')
            else:
                files = directory_path.iterdir()
            
            for file_path in files:
                if file_path.is_file():
                    # Create metadata with relative path info
                    metadata = {
                        "relative_path": str(file_path.relative_to(directory_path)),
                        "parent_directory": str(directory_path)
                    }
                    
                    if self.add_file_to_manifest(file_path, category, provenance, metadata):
                        added_count += 1
            
            log_message(f"Added {added_count} files from directory: {directory_path}", level='INFO')
            
        except Exception as e:
            log_message(f"Error adding directory to manifest {directory_path}: {e}", level='ERROR')
        
        return added_count
    
    def _update_statistics(self):
        """Update manifest statistics."""
        try:
            files = self.evidence_manifest["files"]
            
            # Total counts
            self.evidence_manifest["statistics"]["total_files"] = len(files)
            self.evidence_manifest["statistics"]["total_size_bytes"] = sum(
                f.get("size_bytes", 0) for f in files.values()
            )
            
            # Files by category
            category_counts = {}
            for file_entry in files.values():
                category = file_entry.get("category", "unknown")
                category_counts[category] = category_counts.get(category, 0) + 1
            
            self.evidence_manifest["statistics"]["files_by_category"] = category_counts
            
        except Exception as e:
            log_message(f"Error updating statistics: {e}", level='ERROR')
    
    def save_manifest(self) -> bool:
        """
        Save the evidence manifest to disk.
        
        Returns:
            True if successfully saved, False otherwise
        """
        try:
            with open(self.evidence_manifest_path, 'w', encoding='utf-8') as f:
                json.dump(self.evidence_manifest, f, indent=2, default=str)
            
            log_message(f"Saved evidence manifest: {self.evidence_manifest_path}", level='INFO')
            return True
            
        except Exception as e:
            log_message(f"Error saving evidence manifest: {e}", level='ERROR')
            return False
    
    def get_files_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        Get all files in a specific category.
        
        Args:
            category: Category name
            
        Returns:
            List of file entries for the category
        """
        category_files = []
        
        if category in self.evidence_manifest["categories"]:
            for file_key in self.evidence_manifest["categories"][category]:
                if file_key in self.evidence_manifest["files"]:
                    category_files.append(self.evidence_manifest["files"][file_key])
        
        return category_files
    
    def verify_file_integrity(self, file_key: str) -> bool:
        """
        Verify the integrity of a file by checking its hash.
        
        Args:
            file_key: File key in the manifest
            
        Returns:
            True if file integrity is verified, False otherwise
        """
        if file_key not in self.evidence_manifest["files"]:
            log_message(f"File key not found in manifest: {file_key}", level='ERROR')
            return False
        
        file_entry = self.evidence_manifest["files"][file_key]
        file_path = Path(file_entry["path"])
        expected_hash = file_entry["sha256"]
        
        if not file_path.exists():
            log_message(f"File not found for verification: {file_path}", level='ERROR')
            return False
        
        current_hash = self.compute_file_hash(file_path)
        
        if current_hash == expected_hash:
            log_message(f"File integrity verified: {file_path.name}", level='DEBUG')
            return True
        else:
            log_message(f"File integrity check failed: {file_path.name}", level='ERROR')
            return False
    
    def create_collected_case_json(self, form_data: Dict[str, Any], 
                                 processing_results: Dict[str, Any]) -> bool:
        """
        Create the collected_case.json file for downstream pipeline.
        
        Args:
            form_data: Original form data from submission
            processing_results: Results from patent processing
            
        Returns:
            True if successfully created, False otherwise
        """
        try:
            collected_case = {
                "submission_id": self.submission_id,
                "created_at": datetime.now().isoformat(),
                "form_data": form_data,
                "processing_results": processing_results,
                "evidence_manifest_summary": {
                    "total_files": self.evidence_manifest["statistics"]["total_files"],
                    "files_by_category": self.evidence_manifest["statistics"]["files_by_category"],
                    "manifest_path": str(self.evidence_manifest_path)
                }
            }
            
            with open(self.collected_case_path, 'w', encoding='utf-8') as f:
                json.dump(collected_case, f, indent=2, default=str)
            
            log_message(f"Created collected_case.json: {self.collected_case_path}", level='INFO')
            return True
            
        except Exception as e:
            log_message(f"Error creating collected_case.json: {e}", level='ERROR')
            return False
