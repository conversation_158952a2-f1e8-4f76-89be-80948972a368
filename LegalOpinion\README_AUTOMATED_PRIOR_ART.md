# Automated Prior Art Retrieval System

## Overview

This module implements a comprehensive automated prior art retrieval system for design patent legal opinion processing, following the **Egyptian Goddess** standard. The system combines text and image similarity search with sophisticated scoring and deduplication to identify the most persuasive prior art candidates.

## Key Features

### 🔍 Multi-Modal Search
- **Text Queries**: Semantic search using BGE embeddings in `Patent_Title_bge_small` collection
- **Image Queries**: Visual similarity using SigLIP embeddings in `IP_Assets_Optimized` collection
- **LLM Keyword Generation**: Automatically generates 3 targeted search terms from patent titles and visual descriptions

### 📊 Advanced Scoring System
- **2-Stage Scoring**: Broad retrieval followed by cross-modal scoring
- **Article Matching**: LLM-based Curver legal standard comparison (0-10 scale)
- **Age Bonus**: Older patents receive higher scores (Egyptian Goddess principle)
- **Score Fusion**: Weighted combination of text, image, and bonus scores

### 🎯 Legal Standard Compliance
- **Egyptian Goddess Standard**: Close prior art narrows patent scope
- **Curver Article Matching**: Ensures article-of-manufacture alignment
- **Persuasion Scoring**: 100-point rubric for final candidate ranking

### 🔄 Deduplication & Diversification
- **Visual Deduplication**: DBSCAN clustering to remove near-duplicates
- **Article Diversification**: Maximum 3 candidates per LOC code to avoid monoculture
- **Close Prior Art Selection**: Intelligent selection of 1-3 strongest candidates

## Architecture

```
┌─────────────────────────────────────────────────────────────────┐
│                    AUTOMATED PRIOR ART RETRIEVAL                │
├─────────────────────────────────────────────────────────────────┤
│  Stage 1: LLM Keyword Generation                               │
│  ├─ Input: Patent title, visual description, client title      │
│  └─ Output: 3 targeted search keywords                         │
├─────────────────────────────────────────────────────────────────┤
│  Stage 2: Text Queries (BGE + Qdrant)                         │
│  ├─ Collection: Patent_Title_bge_small                         │
│  ├─ Top-k per query: 20                                        │
│  └─ Text pool cap: 60 candidates                               │
├─────────────────────────────────────────────────────────────────┤
│  Stage 3: Image Queries (SigLIP + Qdrant)                     │
│  ├─ Collection: IP_Assets_Optimized                            │
│  ├─ Top-k per view: 40                                         │
│  └─ Image pool cap: 80 candidates                              │
├─────────────────────────────────────────────────────────────────┤
│  Stage 4: Union & Cross-Scoring                               │
│  ├─ Deduplicate by reg_no                                      │
│  └─ Fill missing modality scores                               │
├─────────────────────────────────────────────────────────────────┤
│  Stage 5: Scoring Pipeline                                     │
│  ├─ Score normalization (min-max per modality)                 │
│  ├─ Article match bonus (LLM + Curver standard)                │
│  ├─ Age bonus (0.2 * years_delta, capped at 1.0)              │
│  └─ Score fusion (0.55*img + 0.35*text + 0.2*bonuses)         │
├─────────────────────────────────────────────────────────────────┤
│  Stage 6: Deduplication & Diversification                     │
│  ├─ DBSCAN clustering for visual deduplication                 │
│  └─ Max 3 candidates per LOC code                              │
├─────────────────────────────────────────────────────────────────┤
│  Stage 7: LLM Persuasion Scoring (100-point rubric)           │
│  ├─ Article match (Curver): 25 pts                             │
│  ├─ Closeness to asserted design: 25 pts                       │
│  ├─ Age advantage: 15 pts                                      │
│  ├─ Figure usefulness: 15 pts                                  │
│  ├─ Diversity potential: 10 pts                                │
│  └─ Simplicity: 10 pts                                         │
├─────────────────────────────────────────────────────────────────┤
│  Stage 8: Final Selection                                      │
│  ├─ Shortlist: Top 20 candidates                               │
│  └─ Close prior art: 1-3 candidates (threshold-based)          │
├─────────────────────────────────────────────────────────────────┤
│  Stage 9: Asset Fetching                                       │
│  ├─ Download figures from USPTO_Grants                         │
│  └─ Create metadata files with scores                          │
└─────────────────────────────────────────────────────────────────┘
```

## Usage

### Basic Usage

```python
from LegalOpinion.automated_prior_art_retrieval import AutomatedPriorArtRetrieval

# Initialize the system
retrieval_system = AutomatedPriorArtRetrieval(
    submission_id="case-001",
    prior_art_dir="/path/to/prior_art"
)

# Perform retrieval
result = await retrieval_system.retrieve_automated_prior_art(
    asserted_patent_data=patent_data,
    product_images=image_paths,
    visual_description="A woven storage container",
    client_title="Client basket design"
)

# Process results
if result["success"]:
    shortlist = result["shortlist"]
    close_prior_art = result["close_prior_art"]
```

### Integration with PatentProcessor

```python
from LegalOpinion.patent_processor import PatentProcessor

# Initialize processor
processor = PatentProcessor(submission_id="case-001", workspace_paths=paths)

# Perform automated prior art retrieval
result = await processor.perform_automated_prior_art_retrieval(
    asserted_patent_data=patent_data,
    product_images_dir="/path/to/images",
    visual_description="Visual description from Curver analysis"
)
```

## Configuration

The system uses the following default configuration:

```python
config = {
    "top_k_text_per_query": 20,      # Results per text query
    "top_k_img_per_view": 40,        # Results per image query
    "text_pool_cap": 60,             # Maximum text candidates
    "image_pool_cap": 80,            # Maximum image candidates
    "final_shortlist_size": 20,      # Final shortlist size
    "close_prior_art_threshold": 80, # Threshold for close prior art
    "close_prior_art_max": 3         # Maximum close prior art candidates
}
```

## Dependencies

### Required Packages
- `qdrant-client`: Vector database client
- `sentence-transformers`: BGE text embeddings
- `scikit-learn`: DBSCAN clustering
- `numpy`: Numerical operations

### Required Collections
- `Patent_Title_bge_small`: Text embeddings collection
- `IP_Assets_Optimized`: Image embeddings collection with SigLIP vectors

### Required Services
- **Qdrant Vector Database**: For similarity search
- **LLM Service**: For keyword generation and scoring (Vertex AI/Gemini)
- **SigLIP Model**: For image embeddings
- **PostgreSQL Database**: For patent metadata

## Output Format

The system returns a comprehensive result dictionary:

```python
{
    "success": bool,
    "submission_id": str,
    "shortlist": [
        {
            "reg_no": str,
            "title": str,
            "llm_persuasion_score": float,  # 0-100
            "fused_score": float,           # 0-1
            "article_bonus": float,         # 0-1
            "age_bonus": float,             # 0-1
            "figure_files": [str],          # Paths to downloaded figures
            "metadata_path": str,           # Path to metadata JSON
            "patent_data": dict             # Full patent information
        }
    ],
    "close_prior_art": [dict],              # Subset of shortlist
    "total_candidates_found": int,
    "processing_time": float,
    "stage_results": dict                   # Detailed stage-by-stage results
}
```

## Legal Standards

### Egyptian Goddess Standard
- **Principle**: Similarity is judged in light of the prior art
- **Impact**: Close prior art narrows the patent's scope
- **Implementation**: Age bonus favors older patents, close prior art selection

### Curver Article-of-Manufacture Test
- **Requirement**: Accused product must be same article or close variant
- **Scoring**: 0-10 scale based on functional role similarity
- **Integration**: Article bonus in scoring pipeline

## Testing

Run the test suite:

```bash
python LegalOpinion/test_automated_prior_art.py
```

Run the example usage:

```bash
python LegalOpinion/example_prior_art_usage.py
```

## Files

- `automated_prior_art_retrieval.py`: Main system implementation
- `test_automated_prior_art.py`: Comprehensive test suite
- `example_prior_art_usage.py`: Usage examples and integration demos
- `README_AUTOMATED_PRIOR_ART.md`: This documentation

## Performance Considerations

- **Parallel Processing**: Text and image queries can run concurrently
- **Caching**: BGE and SigLIP models are loaded once and reused
- **Batch Operations**: Database queries are batched for efficiency
- **Memory Management**: Large embeddings are processed in chunks

## Future Enhancements

1. **Visual Deduplication**: Full DBSCAN implementation with figure embeddings
2. **Cross-Modal Scoring**: Complete implementation of missing modality scoring
3. **Advanced Clustering**: HDBSCAN for better deduplication
4. **Parallel LLM Calls**: Batch processing for article matching and persuasion scoring
5. **Caching Layer**: Redis cache for frequently accessed embeddings
