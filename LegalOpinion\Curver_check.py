"""
Curver Article-of-Manufacture Analysis

Implements article-of-manufacture derivation for design patent scope checking
based on Curver Luxembourg v. Home Expressions precedent.

A design patent's claim is tied to the article of manufacture named in its title/claim;
asserting it against a different article can fail as a matter of law.
"""

import os
import json
from typing import Dict, List, Any, Optional, Tuple
from logdata import log_message
from Common.Constants import Constants
from AI.GC_VertexAI import vertex_genai_multi_async
from IP.Patents_Bulk.patent_db_grant import get_db_connection


class CurverArticleAnalyzer:
    """
    Analyzes article-of-manufacture scope for design patents using Curver precedent.
    
    Combines:
    1. CPC/USPC classification mapping to derive patent article
    2. Visual description of accused product images
    3. LLM-based mismatch analysis applying Curver legal standard
    """
    
    def __init__(self):
        """Initialize the Curver analyzer."""
        self.db_connection = None
    
    async def analyze_article_scope(self, patent_data: Dict[str, Any], 
                                  product_images: List[str],
                                  product_description: str = "") -> Dict[str, Any]:
        """
        Perform complete Curver article-of-manufacture analysis.
        
        Args:
            patent_data: Patent information including reg_no, title, etc.
            product_images: List of product image file paths
            product_description: Optional product description from form
            
        Returns:
            Analysis results with scope determination
        """
        result = {
            "success": False,
            "patent_article": "",
            "product_article": "",
            "scope_mismatch": False,
            "reason": "",
            "cpc_classifications": [],
            "uspc_classifications": [],
            "visual_description": "",
            "error": None
        }
        
        try:
            patent_reg_no = patent_data.get("reg_no", "")
            patent_title = patent_data.get("title", "")
            patents_id = patent_data.get("id", "")
            
            if not patents_id:
                result["error"] = "Patent ID required for classification lookup"
                return result
            
            log_message(f"Starting Curver analysis for patent {patent_reg_no}", level='INFO')
            
            # Step 1: Get CPC/USPC classifications
            cpc_data = await self._get_cpc_classifications(patents_id)
            uspc_data = await self._get_uspc_classifications(patents_id)
            
            result["cpc_classifications"] = cpc_data
            result["uspc_classifications"] = uspc_data
            
            # Step 2: Get visual description of product images
            if product_images:
                visual_desc = await self._get_visual_description(product_images)
                result["visual_description"] = visual_desc
            
            # Step 3: Perform mismatch analysis
            analysis = await self._perform_mismatch_analysis(
                patent_title=patent_title,
                cpc_data=cpc_data,
                uspc_data=uspc_data,
                product_description=product_description,
                visual_description=result["visual_description"]
            )
            
            result.update(analysis)
            result["success"] = True
            
            log_message(f"Curver analysis completed: {result['patent_article']} vs {result['product_article']}, mismatch: {result['scope_mismatch']}", level='INFO')
            
        except Exception as e:
            log_message(f"Error in Curver analysis: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result
    
    async def _get_cpc_classifications(self, patents_id: str) -> List[Dict[str, Any]]:
        """
        Get CPC classifications for a patent from database.
        
        Args:
            patents_id: Patent UUID
            
        Returns:
            List of CPC classification data with definitions
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Join patents_cpc_ipc_assignments with patents_cpc_ipc_definitions
            query = """
                SELECT DISTINCT 
                    d.section, d.class, d.subclass, d.main_group, d.sub_group,
                    d.definition, d.classification_type
                FROM patents_cpc_ipc_assignments a
                JOIN patents_cpc_ipc_definitions d ON a.cpc_ipc_id = d.id
                WHERE a.patents_id = %s 
                    AND d.classification_type = 'CPC'
                    AND d.definition IS NOT NULL
                ORDER BY d.section, d.class, d.subclass, d.main_group, d.sub_group
            """
            
            cursor.execute(query, (patents_id,))
            rows = cursor.fetchall()
            
            cpc_data = []
            for row in rows:
                # Build CPC code (e.g., "D06F37/02")
                cpc_code = row[0]  # section
                if row[1]:  # class
                    cpc_code += row[1]
                if row[2]:  # subclass
                    cpc_code += row[2]
                if row[3]:  # main_group
                    cpc_code += row[3]
                if row[4]:  # sub_group
                    cpc_code += "/" + row[4]
                
                cpc_data.append({
                    "code": cpc_code,
                    "section": row[0],
                    "class": row[1],
                    "subclass": row[2],
                    "main_group": row[3],
                    "sub_group": row[4],
                    "definition": row[5],
                    "specificity_level": self._calculate_cpc_specificity(row)
                })
            
            cursor.close()
            conn.close()
            
            # Sort by specificity (most specific first)
            cpc_data.sort(key=lambda x: x["specificity_level"], reverse=True)
            
            log_message(f"Found {len(cpc_data)} CPC classifications", level='DEBUG')
            return cpc_data[:3]  # Return top 3 most specific
            
        except Exception as e:
            log_message(f"Error getting CPC classifications: {e}", level='ERROR')
            return []
    
    async def _get_uspc_classifications(self, patents_id: str) -> List[Dict[str, Any]]:
        """
        Get USPC classifications for a patent from database.
        
        Args:
            patents_id: Patent UUID
            
        Returns:
            List of USPC classification data with definitions
        """
        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            
            # Join patents_uspc_assignments with patents_uspc_definitions
            query = """
                SELECT DISTINCT 
                    d.class, d.subclass, d.definition, a.type
                FROM patents_uspc_assignments a
                JOIN patents_uspc_definitions d ON a.uspc_id = d.id
                WHERE a.patents_id = %s 
                    AND d.definition IS NOT NULL
                ORDER BY a.type, d.class, d.subclass
            """
            
            cursor.execute(query, (patents_id,))
            rows = cursor.fetchall()
            
            uspc_data = []
            for row in rows:
                # Build USPC code (e.g., "D06/123")
                uspc_code = row[0] if row[0] else ""
                if row[1]:
                    uspc_code += "/" + row[1]
                
                uspc_data.append({
                    "code": uspc_code,
                    "class": row[0],
                    "subclass": row[1],
                    "definition": row[2],
                    "type": row[3],
                    "specificity_level": 2 if row[1] else 1  # subclass more specific than class
                })
            
            cursor.close()
            conn.close()
            
            # Sort by specificity and type (primary first)
            uspc_data.sort(key=lambda x: (x["type"] == "primary", x["specificity_level"]), reverse=True)
            
            log_message(f"Found {len(uspc_data)} USPC classifications", level='DEBUG')
            return uspc_data[:3]  # Return top 3 most specific
            
        except Exception as e:
            log_message(f"Error getting USPC classifications: {e}", level='ERROR')
            return []
    
    def _calculate_cpc_specificity(self, row: Tuple) -> int:
        """Calculate CPC specificity level (higher = more specific)."""
        specificity = 0
        if row[0]:  # section
            specificity += 1
        if row[1]:  # class
            specificity += 1
        if row[2]:  # subclass
            specificity += 1
        if row[3]:  # main_group
            specificity += 1
        if row[4]:  # sub_group
            specificity += 1
        return specificity
    
    async def _get_visual_description(self, product_images: List[str]) -> str:
        """
        Get visual description of product images using LLM.
        
        Args:
            product_images: List of image file paths
            
        Returns:
            1-2 sentence description of the product
        """
        try:
            if not product_images:
                return ""
            
            prompt = """Analyze the product shown in the image(s) and provide a concise 1-2 sentence description focusing on:
1. What type of article/object it is (e.g., "storage basket", "chair", "phone case")
2. Its primary functional role or purpose
3. Key visual characteristics that define its article category

Be specific about the article type - this is for legal analysis of design patent scope."""
            
            prompt_list = [("text", prompt)]
            
            # Add up to 3 images for analysis
            for i, image_path in enumerate(product_images[:3]):
                if os.path.exists(image_path):
                    prompt_list.append(("text", f"\n\nProduct Image {i+1}:"))
                    prompt_list.append(("image_path", image_path))
            
            # Use vertex_genai_multi_async for image analysis
            response = await vertex_genai_multi_async(
                data_list=[prompt_list],
                model_name=Constants.IMAGE_MODEL_FREE,
                useVertexAI=True
            )
            
            if response and len(response) > 0:
                description = response[0].strip()
                log_message(f"Generated visual description: {description[:100]}...", level='DEBUG')
                return description
            
            return ""
            
        except Exception as e:
            log_message(f"Error getting visual description: {e}", level='ERROR')
            return ""
    
    async def _perform_mismatch_analysis(self, patent_title: str,
                                       cpc_data: List[Dict[str, Any]],
                                       uspc_data: List[Dict[str, Any]],
                                       product_description: str,
                                       visual_description: str) -> Dict[str, Any]:
        """
        Perform LLM-based mismatch analysis applying Curver legal standard.
        
        Args:
            patent_title: Patent title
            cpc_data: CPC classification data
            uspc_data: USPC classification data
            product_description: Product description from form
            visual_description: Visual description from images
            
        Returns:
            Analysis results with scope determination
        """
        try:
            # Format classification data for prompt
            cpc_labels = []
            for cpc in cpc_data:
                cpc_labels.append(f"{cpc['code']}: {cpc['definition'][:100]}...")
            
            uspc_labels = []
            for uspc in uspc_data:
                uspc_labels.append(f"{uspc['code']}: {uspc['definition'][:100]}...")
            
            # Construct the detailed prompt
            prompt = f"""You are classifying *article of manufacture* for a U.S. design patent scope check (Curver).

Inputs:
- PATENT_TITLE: "{patent_title}"
- CPC_LABELS: {json.dumps(cpc_labels)}
- USPC_LABELS: {json.dumps(uspc_labels)}
- PRODUCT_DESCRIPTION: "{product_description}"
- VISUAL_DESCRIPTION: "{visual_description}"

Tasks:
1) Return "patent_article": a short noun phrase (e.g., "storage basket", "chair", "phone case").
2) Return "product_article": a short noun phrase for the accused product (or "unknown").
3) Return "scope_mismatch": true/false with reason, applying Curver (match only if the accused product is the same article or a very close variant serving the same functional role).

Decision criterion: Mismatch if the accused product's functional role/article class is not reasonably within the patented article's category (e.g., "storage basket" vs. "chair"). That kind of mismatch led to non-infringement in Curver.

Output strict JSON: {{"patent_article":"","product_article":"","scope_mismatch":bool,"reason":""}}"""
            
            prompt_list = [("text", prompt)]
            
            # Use vertex_genai_multi_async for analysis
            response = await vertex_genai_multi_async(
                data_list=[prompt_list],
                model_name=Constants.TEXT_MODEL_FREE,
                useVertexAI=True
            )
            
            if response and len(response) > 0:
                response_text = response[0].strip()
                
                # Parse JSON response
                try:
                    # Extract JSON from response (handle potential markdown formatting)
                    if "```json" in response_text:
                        json_start = response_text.find("```json") + 7
                        json_end = response_text.find("```", json_start)
                        response_text = response_text[json_start:json_end].strip()
                    elif "```" in response_text:
                        json_start = response_text.find("```") + 3
                        json_end = response_text.find("```", json_start)
                        response_text = response_text[json_start:json_end].strip()
                    
                    analysis_result = json.loads(response_text)
                    
                    # Validate required fields
                    required_fields = ["patent_article", "product_article", "scope_mismatch", "reason"]
                    for field in required_fields:
                        if field not in analysis_result:
                            analysis_result[field] = ""
                    
                    log_message(f"Curver analysis result: {analysis_result}", level='DEBUG')
                    return analysis_result
                    
                except json.JSONDecodeError as e:
                    log_message(f"Failed to parse JSON response: {e}", level='ERROR')
                    log_message(f"Raw response: {response_text}", level='DEBUG')
                    return {
                        "patent_article": "",
                        "product_article": "",
                        "scope_mismatch": False,
                        "reason": f"Failed to parse LLM response: {str(e)}"
                    }
            
            return {
                "patent_article": "",
                "product_article": "",
                "scope_mismatch": False,
                "reason": "No response from LLM analysis"
            }
            
        except Exception as e:
            log_message(f"Error in mismatch analysis: {e}", level='ERROR')
            return {
                "patent_article": "",
                "product_article": "",
                "scope_mismatch": False,
                "reason": f"Analysis error: {str(e)}"
            }


# Convenience function for integration
async def analyze_curver_scope(patent_data: Dict[str, Any], 
                             product_images: List[str],
                             product_description: str = "") -> Dict[str, Any]:
    """
    Convenience function to perform Curver article-of-manufacture analysis.
    
    Args:
        patent_data: Patent information including reg_no, title, id
        product_images: List of product image file paths
        product_description: Optional product description from form
        
    Returns:
        Analysis results with scope determination
    """
    analyzer = CurverArticleAnalyzer()
    return await analyzer.analyze_article_scope(patent_data, product_images, product_description)
