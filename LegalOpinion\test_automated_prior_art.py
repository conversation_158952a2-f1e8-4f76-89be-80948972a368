"""
Test suite for the Automated Prior Art Retrieval System

This module contains comprehensive tests for the automated prior art retrieval system,
including unit tests for individual components and integration tests for the full pipeline.
"""

import os
import sys
import asyncio
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import pytest

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from LegalOpinion.automated_prior_art_retrieval import AutomatedPriorArtRetrieval
from logdata import log_message


class TestAutomatedPriorArtRetrieval:
    """Test class for the AutomatedPriorArtRetrieval system."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)
    
    @pytest.fixture
    def mock_retrieval_system(self, temp_dir):
        """Create a mock retrieval system for testing."""
        with patch.dict(os.environ, {
            'QDRANT_URL': 'http://localhost:6333',
            'QDRANT_API_KEY': 'test-key'
        }):
            with patch('LegalOpinion.automated_prior_art_retrieval.QdrantClient'):
                with patch('LegalOpinion.automated_prior_art_retrieval.SentenceTransformer'):
                    system = AutomatedPriorArtRetrieval(
                        submission_id="test-submission",
                        prior_art_dir=temp_dir
                    )
                    return system
    
    @pytest.fixture
    def sample_patent_data(self):
        """Sample patent data for testing."""
        return {
            "id": "test-uuid",
            "reg_no": "D640227",
            "patent_title": "Storage basket",
            "date_published": "2011-06-21",
            "loc_code": "06-02",
            "uspc_class": "D6",
            "uspc_subclass": "402",
            "folder": "test-folder",
            "fig_files": ["fig1.png", "fig2.png"],
            "inventors": "John Doe",
            "assignee": "Test Company"
        }
    
    @pytest.fixture
    def sample_product_images(self, temp_dir):
        """Create sample product image files for testing."""
        images = []
        for i in range(3):
            image_path = os.path.join(temp_dir, f"product_{i}.jpg")
            # Create empty files for testing
            with open(image_path, 'wb') as f:
                f.write(b'fake image data')
            images.append(image_path)
        return images
    
    @pytest.mark.asyncio
    async def test_generate_search_keywords(self, mock_retrieval_system):
        """Test keyword generation from patent title and description."""
        with patch('LegalOpinion.automated_prior_art_retrieval.vertex_genai_multi_async') as mock_llm:
            mock_llm.return_value = ["storage basket | container basket | woven basket"]
            
            keywords = await mock_retrieval_system.generate_search_keywords(
                patent_title="Storage basket",
                visual_description="A woven storage container"
            )
            
            assert len(keywords) == 3
            assert "storage basket" in keywords
            assert "container basket" in keywords
            assert "woven basket" in keywords
    
    @pytest.mark.asyncio
    async def test_generate_search_keywords_fallback(self, mock_retrieval_system):
        """Test keyword generation fallback when LLM fails."""
        with patch('LegalOpinion.automated_prior_art_retrieval.vertex_genai_multi_async') as mock_llm:
            mock_llm.side_effect = Exception("LLM error")
            
            keywords = await mock_retrieval_system.generate_search_keywords(
                patent_title="Storage basket"
            )
            
            assert len(keywords) == 3
            assert all(kw == "storage basket" for kw in keywords)
    
    @pytest.mark.asyncio
    async def test_perform_text_queries(self, mock_retrieval_system):
        """Test text queries using BGE embeddings."""
        # Mock the BGE model
        mock_retrieval_system.bge_model = Mock()
        mock_retrieval_system.bge_model.encode.return_value = Mock()
        mock_retrieval_system.bge_model.encode.return_value.tolist.return_value = [0.1] * 384
        
        # Mock Qdrant search results
        mock_result = Mock()
        mock_result.score = 0.85
        mock_result.payload = {"reg_no": "D640227"}
        
        mock_retrieval_system.qdrant_client.search.return_value = [mock_result]
        
        keywords = ["storage basket", "container basket", "woven basket"]
        result = await mock_retrieval_system.perform_text_queries(keywords)
        
        assert result["success"] is True
        assert len(result["text_pool"]) > 0
        assert result["text_pool"][0]["reg_no"] == "D640227"
        assert result["text_pool"][0]["score"] == 0.85
    
    @pytest.mark.asyncio
    async def test_perform_image_queries(self, mock_retrieval_system, sample_product_images):
        """Test image queries using SigLIP embeddings."""
        with patch('LegalOpinion.automated_prior_art_retrieval.SiglipModel') as mock_siglip_class:
            mock_siglip = Mock()
            mock_siglip.compute_features.return_value = [Mock()]
            mock_siglip.compute_features.return_value[0].tolist.return_value = [0.1] * 1024
            mock_siglip_class.return_value = mock_siglip
            
            # Mock Qdrant query results
            mock_point = Mock()
            mock_point.score = 0.92
            mock_point.payload = {"reg_no": "D640227"}
            
            mock_result = Mock()
            mock_result.points = [mock_point]
            
            mock_retrieval_system.qdrant_client.query_points.return_value = mock_result
            
            result = await mock_retrieval_system.perform_image_queries(sample_product_images)
            
            assert result["success"] is True
            assert len(result["image_pool"]) > 0
            assert result["image_pool"][0]["reg_no"] == "D640227"
            assert result["image_pool"][0]["score"] == 0.92
    
    @pytest.mark.asyncio
    async def test_create_union_and_cross_score(self, mock_retrieval_system):
        """Test union creation and cross-scoring."""
        text_pool = [
            {"reg_no": "D640227", "score": 0.85, "source": "text_keyword"},
            {"reg_no": "D123456", "score": 0.75, "source": "text_keyword"}
        ]
        
        image_pool = [
            {"reg_no": "D640227", "score": 0.92, "source": "image_sim"},
            {"reg_no": "D789012", "score": 0.88, "source": "image_sim"}
        ]
        
        union = await mock_retrieval_system.create_union_and_cross_score(text_pool, image_pool)
        
        assert len(union) == 3  # D640227, D123456, D789012
        
        # Check that D640227 has both scores
        d640227 = next(c for c in union if c["reg_no"] == "D640227")
        assert d640227["text_score"] == 0.85
        assert d640227["image_score"] == 0.92
        assert not d640227["text_missing"]
        assert not d640227["image_missing"]
        
        # Check that D123456 has only text score
        d123456 = next(c for c in union if c["reg_no"] == "D123456")
        assert d123456["text_score"] == 0.75
        assert d123456["image_score"] == 0.0
        assert not d123456["text_missing"]
        assert d123456["image_missing"]
    
    def test_normalize_scores(self, mock_retrieval_system):
        """Test score normalization."""
        candidates = [
            {"text_score": 0.9, "image_score": 0.8},
            {"text_score": 0.7, "image_score": 0.6},
            {"text_score": 0.5, "image_score": 0.4}
        ]
        
        normalized = mock_retrieval_system.normalize_scores(candidates)
        
        # Check that scores are normalized to 0-1 range
        for candidate in normalized:
            assert 0 <= candidate["text_score_norm"] <= 1
            assert 0 <= candidate["image_score_norm"] <= 1
        
        # Check that highest original score gets normalized to 1
        max_text_candidate = max(normalized, key=lambda x: x["text_score"])
        assert max_text_candidate["text_score_norm"] == 1.0
    
    def test_calculate_age_bonus(self, mock_retrieval_system, sample_patent_data):
        """Test age bonus calculation."""
        candidates = [
            {"reg_no": "D640227", "date_published": "2010-06-21"},  # 1 year older
            {"reg_no": "D123456", "date_published": "2005-06-21"},  # 6 years older
            {"reg_no": "D789012", "date_published": "2012-06-21"}   # 1 year newer
        ]
        
        result = mock_retrieval_system.calculate_age_bonus(sample_patent_data, candidates)
        
        # Check age bonuses
        d640227 = next(c for c in result if c["reg_no"] == "D640227")
        assert d640227["age_bonus"] == 0.2  # 0.2 * 1 year
        
        d123456 = next(c for c in result if c["reg_no"] == "D123456")
        assert d123456["age_bonus"] == 1.0  # Capped at 1.0
        
        d789012 = next(c for c in result if c["reg_no"] == "D789012")
        assert d789012["age_bonus"] == 0.0  # Negative age gets 0
    
    def test_fuse_scores(self, mock_retrieval_system):
        """Test score fusion."""
        candidates = [
            {
                "text_score_norm": 0.8,
                "image_score_norm": 0.9,
                "article_bonus": 0.7,
                "age_bonus": 0.3,
                "text_missing": False,
                "image_missing": False
            },
            {
                "text_score_norm": 0.6,
                "image_score_norm": 0.0,
                "article_bonus": 0.5,
                "age_bonus": 0.2,
                "text_missing": False,
                "image_missing": True
            }
        ]
        
        fused = mock_retrieval_system.fuse_scores(candidates)
        
        # Check fusion formulas
        # Both modalities: 0.55*img + 0.35*text + 0.2*bonuses
        expected_1 = 0.55 * 0.9 + 0.35 * 0.8 + 0.2 * (0.7 + 0.3)
        assert abs(fused[0]["fused_score"] - expected_1) < 0.001
        
        # Text only: 0.7*text + 0.3*bonuses
        expected_2 = 0.7 * 0.6 + 0.3 * (0.5 + 0.2)
        assert abs(fused[1]["fused_score"] - expected_2) < 0.001


def run_tests():
    """Run the test suite."""
    try:
        import pytest
        pytest.main([__file__, "-v"])
    except ImportError:
        log_message("pytest not available, running basic tests", level='WARNING')
        # Run basic tests without pytest
        asyncio.run(basic_test_suite())


async def basic_test_suite():
    """Basic test suite that doesn't require pytest."""
    log_message("Running basic test suite for automated prior art retrieval", level='INFO')
    
    # Test keyword generation
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            with patch.dict(os.environ, {
                'QDRANT_URL': 'http://localhost:6333',
                'QDRANT_API_KEY': 'test-key'
            }):
                with patch('LegalOpinion.automated_prior_art_retrieval.QdrantClient'):
                    with patch('LegalOpinion.automated_prior_art_retrieval.SentenceTransformer'):
                        system = AutomatedPriorArtRetrieval(
                            submission_id="test-submission",
                            prior_art_dir=temp_dir
                        )
                        
                        # Test basic initialization
                        assert system.submission_id == "test-submission"
                        assert system.prior_art_dir == Path(temp_dir)
                        
                        log_message("✅ Basic initialization test passed", level='INFO')
        
        log_message("✅ All basic tests passed", level='INFO')
        
    except Exception as e:
        log_message(f"❌ Test failed: {e}", level='ERROR')
        raise


if __name__ == "__main__":
    run_tests()
