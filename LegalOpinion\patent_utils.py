"""
Patent Utilities

Provides patent number normalization and validation utilities specifically
for legal opinion processing, with focus on US design patents.
"""

import re
from datetime import datetime
from typing import Optional, Dict, Any
from logdata import log_message


def normalize_patent_number(patent_number: str) -> str:
    """
    Normalize patent number for database lookup, specifically handling US design patents.
    
    This function handles various input formats and normalizes them for database queries:
    - US********-S1 -> ******** (removes leading zeros after D)
    - US ******** -> ********
    - ******** -> D1234 (removes leading zeros)
    - 1000051 -> 1000051 (utility patents, no D prefix)
    
    Args:
        patent_number: Raw patent number from form input
        
    Returns:
        Normalized patent number for database lookup
        
    Examples:
        >>> normalize_patent_number("US********-S1")
        "********"
        >>> normalize_patent_number("US********")
        "D1234"
        >>> normalize_patent_number("US D 001234")
        "D1234"
        >>> normalize_patent_number("1000051")
        "1000051"
    """
    if not patent_number:
        return ""
    
    # Clean up the input - remove spaces, commas, and common separators
    cleaned = str(patent_number).strip().replace(" ", "").replace(",", "").upper()
    
    # Remove common suffixes like -S1, -B1, -B2, -A1
    cleaned = re.sub(r'-[A-Z]\d*$', '', cleaned)
    
    # Handle various US design patent formats
    if cleaned.startswith("USD"):
        # US******** -> ********, then remove leading zeros
        design_part = cleaned[3:]  # Remove "USD"
        # Remove leading zeros but keep at least one digit
        design_part = re.sub(r'^0+(?=\d)', '', design_part)
        return f"D{design_part}"
    
    elif cleaned.startswith("US") and "D" in cleaned:
        # US ******** or US******** variants
        # Extract the D and number part
        match = re.search(r'D(\d+)', cleaned)
        if match:
            design_number = match.group(1)
            # Remove leading zeros but keep at least one digit
            design_number = re.sub(r'^0+(?=\d)', '', design_number)
            return f"D{design_number}"
    
    elif cleaned.startswith("D"):
        # ******** or ********
        design_part = cleaned[1:]  # Remove "D"
        # Remove leading zeros but keep at least one digit
        design_part = re.sub(r'^0+(?=\d)', '', design_part)
        return f"D{design_part}"
    
    else:
        # Assume utility patent - just remove leading zeros
        # Extract first sequence of digits
        match = re.search(r'\d+', cleaned)
        if match:
            number_part = match.group()
            # Remove leading zeros but keep at least one digit
            number_part = re.sub(r'^0+(?=\d)', '', number_part)
            return number_part
    
    # If no pattern matches, return the cleaned input
    log_message(f"Could not normalize patent number: {patent_number} -> {cleaned}", level='WARNING')
    return cleaned


def is_design_patent(patent_number: str) -> bool:
    """
    Check if a patent number represents a design patent.
    
    Args:
        patent_number: Patent number to check
        
    Returns:
        True if it's a design patent, False otherwise
    """
    if not patent_number:
        return False
    
    cleaned = str(patent_number).strip().upper()
    return (cleaned.startswith("USD") or 
            cleaned.startswith("D") or 
            ("US" in cleaned and "D" in cleaned))


def extract_patent_numbers_from_text(text: str) -> list[str]:
    """
    Extract patent numbers from text using regex patterns.
    
    Args:
        text: Text to search for patent numbers
        
    Returns:
        List of found patent numbers
    """
    if not text:
        return []
    
    # Pattern for US patents including design patents
    # Matches: USD1234567, US D1234567, D1234567, US1234567, 1234567
    pattern = r'(?:USD?|US\s*D?)\s*(\d{1,8})(?:-[A-Z]\d*)?|(?<!\w)D\s*(\d{1,8})(?:-[A-Z]\d*)?|(?<!\w)(\d{7,8})(?:-[A-Z]\d*)?'
    
    matches = re.findall(pattern, text, re.IGNORECASE)
    
    # Flatten the tuple results and filter out empty strings
    patent_numbers = []
    for match in matches:
        for group in match:
            if group:
                patent_numbers.append(group)
    
    return list(set(patent_numbers))  # Remove duplicates


def validate_patent_number_format(patent_number: str) -> Dict[str, Any]:
    """
    Validate patent number format and provide detailed feedback.
    
    Args:
        patent_number: Patent number to validate
        
    Returns:
        Dictionary with validation results:
        {
            "valid": bool,
            "normalized": str,
            "type": str,  # "design", "utility", "unknown"
            "message": str
        }
    """
    if not patent_number or not patent_number.strip():
        return {
            "valid": False,
            "normalized": "",
            "type": "unknown",
            "message": "Patent number is required"
        }
    
    try:
        normalized = normalize_patent_number(patent_number)
        
        if not normalized:
            return {
                "valid": False,
                "normalized": "",
                "type": "unknown", 
                "message": "Could not normalize patent number"
            }
        
        # Determine patent type
        if normalized.startswith("D"):
            patent_type = "design"
            # Design patents should have 6-7 digits after D
            number_part = normalized[1:]
            if not number_part.isdigit() or len(number_part) < 3 or len(number_part) > 7:
                return {
                    "valid": False,
                    "normalized": normalized,
                    "type": patent_type,
                    "message": f"Invalid design patent number format: {normalized}"
                }
        else:
            patent_type = "utility"
            # Utility patents should have 7-8 digits
            if not normalized.isdigit() or len(normalized) < 6 or len(normalized) > 8:
                return {
                    "valid": False,
                    "normalized": normalized,
                    "type": patent_type,
                    "message": f"Invalid utility patent number format: {normalized}"
                }
        
        return {
            "valid": True,
            "normalized": normalized,
            "type": patent_type,
            "message": f"Valid {patent_type} patent number"
        }
        
    except Exception as e:
        log_message(f"Error validating patent number {patent_number}: {e}", level='ERROR')
        return {
            "valid": False,
            "normalized": "",
            "type": "unknown",
            "message": f"Validation error: {str(e)}"
        }


def format_patent_display(patent_number: str) -> str:
    """
    Format patent number for display purposes.
    
    Args:
        patent_number: Normalized patent number
        
    Returns:
        Formatted patent number for display
        
    Examples:
        >>> format_patent_display("********")
        "US D1,000,051"
        >>> format_patent_display("1000051")
        "US 1,000,051"
    """
    if not patent_number:
        return ""
    
    try:
        if patent_number.startswith("D"):
            # Design patent: ******** -> US D1,000,051
            number_part = patent_number[1:]
            if number_part.isdigit():
                formatted_number = f"{int(number_part):,}"
                return f"US D{formatted_number}"
        else:
            # Utility patent: 1000051 -> US 1,000,051
            if patent_number.isdigit():
                formatted_number = f"{int(patent_number):,}"
                return f"US {formatted_number}"
        
        # If formatting fails, return as-is with US prefix
        return f"US {patent_number}"
        
    except (ValueError, TypeError):
        return patent_number


def get_patent_country(patent_number: str) -> str:
    """
    Determine the country of origin for a patent number.
    
    Args:
        patent_number: Patent number to analyze
        
    Returns:
        Country code (e.g., "US", "EP", "JP") or "UNKNOWN"
    """
    if not patent_number:
        return "UNKNOWN"
    
    cleaned = str(patent_number).strip().upper()
    
    # US patents
    if (cleaned.startswith("US") or 
        cleaned.startswith("D") or 
        cleaned.isdigit()):
        return "US"
    
    # European patents
    if cleaned.startswith("EP"):
        return "EP"
    
    # Japanese patents
    if cleaned.startswith("JP"):
        return "JP"
    
    # Add more country patterns as needed
    
    return "UNKNOWN"


def compare_patent_dates(client_patent_date: str, asserted_patent_date: str) -> Dict[str, Any]:
    """
    Compare client patent date with asserted patent date to determine if client patent is prior art.

    Args:
        client_patent_date: Client patent date (various formats)
        asserted_patent_date: Asserted patent date (various formats)

    Returns:
        Dictionary with comparison results:
        {
            "is_prior_to_asserted": bool,
            "client_date_parsed": str or None,
            "asserted_date_parsed": str or None,
            "days_difference": int or None,
            "error": str or None
        }
    """
    result = {
        "is_prior_to_asserted": False,
        "client_date_parsed": None,
        "asserted_date_parsed": None,
        "days_difference": None,
        "error": None
    }

    try:
        # Parse client patent date
        client_dt = parse_patent_date(client_patent_date)
        if not client_dt:
            result["error"] = f"Could not parse client patent date: {client_patent_date}"
            return result
        result["client_date_parsed"] = client_dt.isoformat()

        # Parse asserted patent date
        asserted_dt = parse_patent_date(asserted_patent_date)
        if not asserted_dt:
            result["error"] = f"Could not parse asserted patent date: {asserted_patent_date}"
            return result
        result["asserted_date_parsed"] = asserted_dt.isoformat()

        # Compare dates
        result["is_prior_to_asserted"] = client_dt < asserted_dt
        result["days_difference"] = (asserted_dt - client_dt).days

    except Exception as e:
        result["error"] = f"Error comparing dates: {e}"
        log_message(f"Error comparing patent dates: {e}", level='ERROR')

    return result


def parse_patent_date(date_str: str) -> Optional[datetime]:
    """
    Parse patent date from various formats.

    Args:
        date_str: Date string in various formats

    Returns:
        Parsed datetime object or None if parsing fails
    """
    if not date_str:
        return None

    # Common date formats for patents
    date_formats = [
        "%Y-%m-%d",      # 2011-06-21
        "%m/%d/%Y",      # 06/21/2011
        "%d/%m/%Y",      # 21/06/2011
        "%Y%m%d",        # 20110621
        "%B %d, %Y",     # June 21, 2011
        "%b %d, %Y",     # Jun 21, 2011
        "%d %B %Y",      # 21 June 2011
        "%d %b %Y",      # 21 Jun 2011
    ]

    date_str = str(date_str).strip()

    for fmt in date_formats:
        try:
            return datetime.strptime(date_str, fmt)
        except ValueError:
            continue

    # Try to extract year-month-day from longer strings
    import re

    # Look for YYYY-MM-DD pattern
    match = re.search(r'(\d{4})-(\d{1,2})-(\d{1,2})', date_str)
    if match:
        try:
            year, month, day = match.groups()
            return datetime(int(year), int(month), int(day))
        except ValueError:
            pass

    # Look for YYYYMMDD pattern
    match = re.search(r'(\d{8})', date_str)
    if match:
        try:
            date_num = match.group(1)
            year = int(date_num[:4])
            month = int(date_num[4:6])
            day = int(date_num[6:8])
            return datetime(year, month, day)
        except ValueError:
            pass

    log_message(f"Could not parse date: {date_str}", level='WARNING')
    return None
