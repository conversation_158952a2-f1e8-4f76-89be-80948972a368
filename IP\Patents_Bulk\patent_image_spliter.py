import os
from pathlib import Path
import sys
import cv2
import numpy as np
import re
from tqdm import tqdm
import multiprocessing as mp
import functools
from PIL import Image, ImageOps
import io

# RECT_DIR = "./FilesAllWithRect"
# Path(RECT_DIR).mkdir(exist_ok=True)
DEBUG_DIR = "./FilesDebug"
debug_subfolders = ["0", "1", "HV", "2a", "2b", "2g", "1g", "3g"]
for folder in debug_subfolders:
    (Path(DEBUG_DIR) / folder).mkdir(parents=True, exist_ok=True)
     
_PADDLE_OCR_SINGLETON = None

def get_paddle_ocr():
    """
    Return a singleton PaddleOCR instance to avoid re-loading the model.
    Detects the device automatically (GPU if available, else CPU) on first use.
    Includes its own error handling and helpful guidance.
    """
    global _PADDLE_OCR_SINGLETON

    if _PADDLE_OCR_SINGLETON is not None:
        return _PADDLE_OCR_SINGLETON

    try:
        import paddle
        from paddleocr import PaddleOCR

        use_gpu = paddle.is_compiled_with_cuda()
        if not use_gpu:
            print("No NVIDIA GPU detected or paddlepaddle-gpu not installed. Falling back to CPU.")
        device = "gpu:0" if use_gpu else "cpu"
        print(f"Initializing PaddleOCR on {device}...")

        _PADDLE_OCR_SINGLETON = PaddleOCR(
            device=device,
            lang="en",
            use_doc_orientation_classify=False,
            use_doc_unwarping=False,
            use_textline_orientation=True,
            text_det_limit_side_len=960,
            text_det_limit_type="max",
            enable_hpi=False,
        )
        return _PADDLE_OCR_SINGLETON
    except Exception as e:
        import sys
        print("\n---", file=sys.stderr)
        print(f"Failed to initialize PaddleOCR: {e}.", file=sys.stderr)
        try:
            import paddle  # noqa: F401
            if paddle.is_compiled_with_cuda():
                print("'paddlepaddle-gpu' is present but initialization failed. Check CUDA/cuDNN versions.", file=sys.stderr)
            else:
                print("'paddlepaddle' detected without GPU support. CPU mode attempted but failed.", file=sys.stderr)
        except Exception:
            print("Paddle not importable. Please install 'paddlepaddle' or 'paddlepaddle-gpu' and 'paddleocr'.", file=sys.stderr)
        print("Ensure 'paddleocr' is installed and versions are compatible.", file=sys.stderr)
        print("---\n", file=sys.stderr)
        raise


# --- Helper Functions ---
def save_tiff_image(img, path=None, dpi=(300, 300), miniswhite=False):
    # Processes an image and saves it as a TIFF. If path is provided, it saves to disk. If path is None, it returns the TIFF image as bytes.

    # 2) Otsu binarize (0 or 255)
    _, binary = cv2.threshold(img, 0, 255, cv2.THRESH_BINARY | cv2.THRESH_OTSU)

    # 3) To PIL and ensure true 1-bit without dithering
    pil = Image.fromarray(binary)

    # USPTO-style fax images are usually "miniswhite" (white=0). Invert then convert to 1-bit.
    if miniswhite:
        pil = ImageOps.invert(pil.convert("L")).convert("1", dither=Image.NONE)
        photometric = "miniswhite"
    else:
        pil = pil.convert("1", dither=Image.NONE)
        photometric = "minisblack"

    # 4) Save as TIFF, CCITT Group 4
    if path:
        pil.save(path, format="TIFF", compression="group4", photometric=photometric, dpi=dpi)
        return None
    else:
        buffer = io.BytesIO()
        pil.save(buffer, format="TIFF", compression="group4", photometric=photometric, dpi=dpi)
        return buffer.getvalue()
    

def save_debug_image(img, path_in, folder):
    """Saves an image to a debug subfolder."""
    debug_path = Path(DEBUG_DIR) / folder / path_in.name
    if path_in.suffix.lower() in ['.tiff', '.tif']:
        save_tiff_image(img, debug_path)
    else:
        cv2.imencode(path_in.suffix, img)[1].tofile(str(debug_path))


def is_vertical(rect):
    """Checks if a rectangle is oriented vertically."""
    width = rect[1][0] - rect[0][0]
    height = rect[2][1] - rect[1][1]
    return height > width

def rotate_image(img, rects):
    """Rotates an image 90 degrees clockwise and adjusts rectangle coordinates."""
    # Get the original dimensions BEFORE rotation
    h_orig, w_orig = img.shape[:2]

    # Rotate the image
    img = cv2.rotate(img, cv2.ROTATE_90_CLOCKWISE)

    new_rects = []
    for rect in rects:
        # For each point p=(x, y) in the original rectangle, calculate the new coordinates
        # using the original height (h_orig).
        # new_x = h_orig -1- y  (because Y = 0 is at the top, not at the bottom)
        #We subtract 1 because pixel coordinates are 0-indexed. If the height is h_orig, 
        # the y-coordinates range from 0 to h_orig - 1. This -1 ensures the new coordinates stay within the correct bounds.
        # new_y = x
        new_rect = np.array([(h_orig -1- p[1], p[0]) for p in rect])
        new_rects.append(new_rect)
        
    return img, new_rects

def is_line_white(line, vertical=False):
    """Checks if a line of pixels is entirely white."""
    if vertical:
        return np.all(line >= 250)
    return np.all(line >= 250)

def adjust_rects(rects, dx, dy):
    """Adjusts the coordinates of rectangles by a given delta."""
    new_rects = []
    for rect in rects:
        new_rect = np.array(rect) - [dx, dy]
        new_rects.append(new_rect)
    return new_rects

def process_image(img, rects, path_in, out_root, debug_root, suffix="", return_bytes=False):
    """
    Processes an image with a known number of FIGs.
    This function is designed to be called recursively.
    """
    if len(rects) == 1:
        return handle_one_fig(img, rects, path_in, out_root, debug_root, suffix=suffix, return_bytes=return_bytes)
    elif len(rects) == 2:
        return handle_two_figs(img, rects, path_in, out_root, debug_root, suffix=suffix, return_bytes=return_bytes)
    elif len(rects) > 2:
        return handle_many_figs(img, rects, path_in, out_root, debug_root, suffix=suffix, return_bytes=return_bytes)
    return []


def valid_image_ext(p: Path):
    return p.suffix.lower() in {".png", ".jpg", ".jpeg", ".bmp", ".tif", ".tiff", ".webp"}


def handle_one_fig(img, rects, path_in, out_root, debug_root, suffix="", return_bytes=False):
    if img is None or img.shape[0] == 0 or img.shape[1] == 0:
        return []
    rect = rects[0]
    if is_vertical(rect):
        img, rects = rotate_image(img, rects)
        rect = rects[0]

    x0, y0 = rect.min(axis=0)
    x1, y1 = rect.max(axis=0)

    # Just whiten out the FIG rectangle
    cv2.rectangle(img, (x0, y0), (x1, y1), (255, 255, 255), -1)

    # Find all non-white pixels after whitening the rectangle
    coords = cv2.findNonZero(255 - img)

    if coords is None:
        # If no non-white pixels are found, the image is essentially empty after removing the FIG text.
        # In this case, we should not return an image.
        return []

    x, y, w, h = cv2.boundingRect(coords)
    
    # Add 5 pixel padding, but don't go outside the original image dimensions.
    x_start = max(0, x - 5)
    y_start = max(0, y - 5)
    x_end = min(img.shape[1], x + w + 5)
    y_end = min(img.shape[0], y + h + 5)
    
    img = img[y_start:y_end, x_start:x_end]

    if img.shape[0] == 0 or img.shape[1] == 0:
        # After cropping, if the image is empty, return nothing
        return []

    if return_bytes:
        # The img is GRAY, so weadd 3rd channel (for Siglip)
        return [img]
    else:
        # Save cropped image to file
        out_path = out_root / f"{path_in.stem}{suffix}{path_in.suffix}"
        if path_in.suffix.lower() in ['.tiff', '.tif']:
            save_tiff_image(img, out_path)
        else:
            cv2.imencode(path_in.suffix, img)[1].tofile(str(out_path))
        return []


def handle_two_figs(img, rects, path_in, out_root, debug_root, suffix="", return_bytes=False):
    if img is None or img.shape[0] == 0 or img.shape[1] == 0:
        return []
    are_vertical = [is_vertical(r) for r in rects]
    if any(are_vertical) and not all(are_vertical):
        if not return_bytes:
            save_debug_image(img, path_in, "HV")
        return []
    
    if all(are_vertical):
        img, rects = rotate_image(img, rects)

    rects = sorted(rects, key=lambda r: r.min(axis=0)[1]) # Sort by Y
    
    r1_x0, r1_y0 = rects[0].min(axis=0)
    r1_x1, r1_y1 = rects[0].max(axis=0)
    r2_x0, r2_y0 = rects[1].min(axis=0)
    r2_x1, r2_y1 = rects[1].max(axis=0)

    h, w = img.shape[:2]
    x_margin = w * 0.25
    y_margin = h * 0.25
    cond1=False
    cond2 = False

    # Vertically aligned: the X values are closer to each other than the Y value (but max 25% of the image)
    if abs(r1_x0 - r2_x0) < abs(r1_y0 - r2_y0) and abs(r1_x0 - r2_x0) < x_margin and abs(r1_x1 - r2_x1) < x_margin:
        #Bringing back the old logic
        # Determine if FIGs are above or below their content
        is_top_case = r1_y0 < 5 or np.all(img[0:r1_y0-3, :] >= 250)
        
        #when place h-5, US09283045-20160315-D00028 doesn't pass the bottom case, Ask Serge
        is_bottom_case = r2_y1 > h - 5 or np.all(img[r2_y1+3:, :] >= 250) 

        #if Figs are  either at the bottom or at the top, we make cond1 true
        if is_bottom_case or is_top_case:
            cond1=True
    
        split_y = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = r1_y1 + i
                if line_y >= r2_y0 or line_y >= h: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = r2_y0 - i
                if line_y <= r1_y1: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        
        if split_y != -1:
            img1 = img[:split_y, :]
            img2 = img[split_y:, :]
            results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix="_1", return_bytes=return_bytes)
            adjusted_rect = adjust_rects([rects[1]], 0, split_y)
            results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix="_2", return_bytes=return_bytes)
            return results1 + results2

    # Horizontally aligned: the Y values are closer to each other than the X value (but max 25% of the image)
    elif abs(r1_y0 - r2_y0) < abs(r1_x0 - r2_x0) and abs(r1_y0 - r2_y0) < y_margin and abs(r1_y1 - r2_y1) < y_margin:
        cond2 = True
        # Sort by X to correctly assign rects to sub-images
        rects = sorted(rects, key=lambda r: r.min(axis=0)[0])
        r1_x_end = rects[0].max(axis=0)[0] # end x of rectangle on the left
        r2_x_start = rects[1].min(axis=0)[0] # start x of rectangle on the right
        mid_x = (r1_x_end + r2_x_start) // 2  # take middle between these 2 x coordinates

        stop_point = r2_x_start - mid_x
        for i in range(stop_point): 
            #move towards right         
            if is_line_white(img[:, mid_x + i], vertical=True):
                    img1 = img[:, :mid_x + i]
                    img2 = img[:, mid_x + i:]
                    results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                    adjusted_rect = adjust_rects([rects[1]], mid_x + i, 0)
                    results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                    return results1 + results2
            #move towards left
            elif is_line_white(img[:, mid_x - i], vertical=True):
                    img1 = img[:, :mid_x - i]
                    img2 = img[:, mid_x - i:]
                    results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                    adjusted_rect = adjust_rects([rects[1]], mid_x - i, 0)
                    results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                    return results1 + results2
      

    #If figs are not vertically aligned or horizontally aligned but we can still see a possible split        
    if not(cond1 and cond2):
        # Looking for a Vertically white line, to split left and right
        vertical_line_found = False
        rects_sorted_by_x = sorted(rects, key=lambda r: r.min(axis=0)[0])
        mid_point = w // 2
        r1_x_end = rects_sorted_by_x[0].max(axis=0)[0] # end x of rectangle on the left
        r2_x_start = rects_sorted_by_x[1].min(axis=0)[0] # start x of rectangle on the right
        left_stop_point = abs(mid_point- r1_x_end)
        right_stop_point = abs(r2_x_start - mid_point)
        
        # 1. Looking for a vertical white line, we move towards left
        for i in range(left_stop_point): 
            if is_line_white(img[:, mid_point - i], vertical=True):
                vertical_line_found = True
                img1 = img[:, :mid_point - i]
                img2 = img[:, mid_point- i:]
                results1 = process_image(img1, [rects_sorted_by_x[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                adjusted_rect = adjust_rects([rects_sorted_by_x[1]], mid_point - i, 0)
                results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                return results1 + results2
        
        # 2. Looking for a vertical white line, we move towards right
        if not vertical_line_found:        
            for i in range(right_stop_point):
                if is_line_white(img[:, mid_point + i], vertical=True):
                    vertical_line_found = True
                    img1 = img[:, :mid_point + i]
                    img2 = img[:, mid_point + i:]
                    results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                    adjusted_rect = adjust_rects([rects[1]], mid_point + i, 0)
                    results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                    return results1 + results2
                
        # Looking for a horizontal white line, to split up and down
        if not vertical_line_found:
            horizontal_line_found = False
            rects = sorted(rects, key=lambda r: r.min(axis=0)[1])
            mid_y = (r1_y1 + r2_y0) // 2
            search_range = r2_y0 - mid_y
            
            # 3. Looking for a horizontal white line, we move towards up
            for i in range(search_range):
                if mid_y - i > r1_y1 and is_line_white(img[mid_y - i, :]):
                    horizontal_line_found = True
                    img1 = img[ :mid_y- i,:]
                    img2 = img[mid_y - i:,:]
                    results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                    adjusted_rect = adjust_rects([rects[1]],0,mid_y-i)
                    results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                    return results1 + results2
                
            # 4. Looking for a horizontal white line, we move towards down
            if not horizontal_line_found:
                for i in range(search_range):
                    if mid_y + i < r2_y0 and mid_y + i < h and is_line_white(img[mid_y + i, :]):
                        horizontal_line_found = True
                        img1 = img[ :mid_y+i,:]
                        img2 = img[mid_y +i:,:]
                        results1 = process_image(img1, [rects[0]], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
                        adjusted_rect = adjust_rects([rects[1]],0,mid_y+i)
                        results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
                        return results1 + results2
            #We finally place an image in a debug folder after all the above checks
            if not return_bytes:
                save_debug_image(img, path_in, "2b")
    return []



def handle_many_figs(img, rects, path_in, out_root, debug_root, suffix="", return_bytes=False):
    if img is None or img.shape[0] == 0 or img.shape[1] == 0:
        return []
    are_vertical = [is_vertical(r) for r in rects]
    if any(are_vertical) and not all(are_vertical):
        if not return_bytes:
            save_debug_image(img, path_in, "HV")
        return []
    
    if all(are_vertical):
        img, rects = rotate_image(img, rects)

    h, w = img.shape[:2]
    y_margin = h * 0.25

    # Group by Y coordinate
    rects = sorted(rects, key=lambda r: r.min(axis=0)[1])
    groups = []
    if rects:
        current_group = [rects[0]]
        for i in range(1, len(rects)):
            #Implementing the same updated logic as in handle_two_figs for grouping HA figs
            r1_x0, r1_y0 = rects[i].min(axis=0)
            r1_x1, r1_y1 = rects[i].max(axis=0)
            r2_x0, r2_y0 = current_group[-1].min(axis=0)
            r2_x1, r2_y1 = current_group[-1].max(axis=0)
            if abs(r1_y0 - r2_y0) < abs(r1_x0 - r2_x0) and abs(r1_y0 - r2_y0) < y_margin and abs(r1_y1 - r2_y1) < y_margin:
                current_group.append(rects[i])
            else:
                groups.append(current_group)
                current_group = [rects[i]]
        groups.append(current_group)

    if len(groups) == 1: # One row of figures
        group = sorted(groups[0], key=lambda r: r.min(axis=0)[0])
        split_points = [0]
        for i in range(len(group) - 1):
            x_start = group[i].max(axis=0)[0] # end x of rectangle on the left
            x_end = group[i+1].min(axis=0)[0] # start x of rectangle on the right
            mid_x = (x_start + x_end) // 2  # take middle between these 2 x coordinates
            split_x = -1
            # The search range is from the middle point to the start of the right rectangle.
            search_range = x_end - mid_x
            for i in range(search_range):
                # Check right
                if mid_x + i < x_end and is_line_white(img[:, mid_x + i], vertical=True):
                    split_x = mid_x + i
                    break
                # Check left
                if mid_x - i > x_start and is_line_white(img[:, mid_x - i], vertical=True):
                    split_x = mid_x - i
                    break
            
            if split_x != -1:
                split_points.append(split_x)
            else:
                # If no split point is found between two rects, debug and stop.
                if not return_bytes:
                    save_debug_image(img, path_in, "1g")
                return []
        split_points.append(w)
        
        all_results = []
        for i in range(len(split_points) - 1):
            sub_img = img[:, split_points[i]:split_points[i+1]]
            adjusted_rect = adjust_rects([group[i]], split_points[i], 0)
            results = process_image(sub_img, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}", return_bytes=return_bytes)
            all_results.extend(results)
        return all_results

    elif len(groups) == 2: # Two rows of figures
        #Bringing back the same old logic for splitting VA figs

        top_group_top_y = groups[0][0].min(axis=0)[1] # top of first rectangle in first row
        bottom_group_bottom_y = groups[1][0].max(axis=0)[1] #bottom of the last rectangle in second row
        is_top_case = top_group_top_y < 5 or np.all(img[0:max(0,top_group_top_y-4), :] >= 240)
        is_bottom_case = bottom_group_bottom_y > h - 5 or np.all(img[min(h,bottom_group_bottom_y+4):, :] >= 240)
        if is_bottom_case or is_top_case:
            cond1=True
        split_y = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = groups[0][-1].max(axis=0)[1] + i
                if line_y >= groups[1][0].min(axis=0)[1] or line_y >= h: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = groups[1][0].min(axis=0)[1] - i
                if line_y <= groups[0][-1].max(axis=0)[1]: break
                if is_line_white(img[line_y, :]):
                    split_y = line_y
                    break
        
        if split_y != -1:
            img1 = img[:split_y, :]
            img2 = img[split_y:, :]
            results1 = process_image(img1, groups[0], path_in, out_root, debug_root, suffix="_1", return_bytes=return_bytes)
            adjusted_rect = adjust_rects(groups[1], 0, split_y)
            results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix="_2", return_bytes=return_bytes)
            return results1 + results2
        
        else:
            if not return_bytes:
                save_debug_image(img, path_in, "2g")
            return []

    elif len(groups) == 3: # Three rows of figures
        # Determine if FIGs are above or below their content
        r1_y0=groups[0][-1].min(axis=0)[1] #top of first rectangle
        r1_y1 = groups[0][-1].max(axis=0)[1] #bottom of first rectangle

        r2_y0 = groups[1][0].min(axis=0)[1] #top of second rectangle
        r2_y1=groups[1][0].max(axis=0)[1] #bottom of second rectangle

        r3_y0 = groups[2][0].min(axis=0)[1] #top of third rectangle
        r3_y1 = groups[2][0].max(axis=0)[1] #bottom of third rectangle

        
        is_top_case = r1_y0 < 5 or np.all(img[0:max(0,r1_y0-4), :] >= 240)
        is_bottom_case = r3_y1 > h - 5 or np.all(img[min(h,r3_y1+4):, :] >= 240)

        split_y1 = -1
        split_y2 = -1
        
        if is_bottom_case: # Labels are below figures, split is after first figure
            for i in range(1, 40):
                line_y = r1_y1 + i
                if line_y >= r2_y0 or line_y >= h: break
                if is_line_white(img[line_y, :]):
                    split_y1 = line_y
                    break
            for i in range(1, 40):
                line_y = r2_y1 + i
                if line_y >= r3_y0 or line_y >= h: break
                if is_line_white(img[line_y, :]):
                    split_y2 = line_y
                    break
            
        elif is_top_case: # Labels are above figures, split is before second figure
            for i in range(1, 40):
                line_y = r2_y0 - i
                if line_y <= r1_y1: break
                if is_line_white(img[line_y, :]):
                    split_y1 = line_y
                    break
            for i in range(1, 40):
                line_y = r3_y0 - i
                if line_y <= r2_y1: break
                if is_line_white(img[line_y, :]):
                    split_y2 = line_y
                    break
        
        if split_y1 != -1 and split_y2 != -1:
            img1 = img[:split_y1, :]
            img2 = img[split_y1:split_y2, :]
            img3 = img[split_y2:, :]
            results1 = process_image(img1, groups[0], path_in, out_root, debug_root, suffix=f"{suffix}_1", return_bytes=return_bytes)
            adjusted_rect = adjust_rects(groups[1], 0, split_y1)
            results2 = process_image(img2, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_2", return_bytes=return_bytes)
            adjusted_rects_3 = adjust_rects(groups[2], 0, split_y2)
            results3 = process_image(img3, adjusted_rects_3, path_in, out_root, debug_root, suffix=f"{suffix}_3", return_bytes=return_bytes)
            return results1 + results2 + results3
        else:
            # If we can't determine top/bottom or find a split, save for debug
            if not return_bytes:
                save_debug_image(img, path_in, "3g")
            return []


    # for more than 3 groups (vertically aligned figs )        
    elif (len(groups) >= 4):
        split_points = [0]
        top_first_rect=rects[0].min(axis=0)[1] #top of first rectangle

        bottom_last_rect = rects[-1].max(axis=0)[1] #bottom of last rectangle
        #Bringing back the old logic
        is_top_case = top_first_rect < 5 or np.all(img[0:max(0,top_first_rect-4),:] >= 240) 
        is_bottom_case = bottom_last_rect > h - 5 or np.all(img[min(h,bottom_last_rect+4):, :] >= 240)
        
        all_results = []
        for i in range(len(groups)-1): #should be traversing groupwise instead of rectangle wise as no.of split_points = no of groups
            # Determine if FIGs are above or below their content
            r1_y1 = groups[i][-1].max(axis=0)[1] #bottom of first rectangle

            r2_y0 = groups[i+1][-1].min(axis=0)[1] #top of second rectangle
                        
            split_y = -1
            
            if is_bottom_case: # Labels are below figures, split is after first figure
                for j in range(1, 40):
                    line_y = r1_y1 + j
                    if line_y >= r2_y0 or line_y >= h: break
                    if is_line_white(img[line_y, :]):
                        split_y = line_y
                        split_points.append(split_y)
                        break
        
                
            elif is_top_case: # Labels are above figures, split is before second figure
                for j in range(1, 40):
                    line_y = r2_y0 - j
                    if line_y <= r1_y1: break
                    if is_line_white(img[line_y, :]):
                        split_y = line_y
                        split_points.append(split_y)
                        break
                
            
            if split_y != -1 and i <= len(groups) - 3:
                sub_img = img[split_points[-2]:split_points[-1], :]
                adjusted_rect = adjust_rects(groups[i], 0,split_points[-2])
                results = process_image(sub_img, adjusted_rect, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}", return_bytes=return_bytes)
                all_results.extend(results)
            elif split_y != -1 and i == len(groups) - 2:# There are only 2 group left: this group and the last group (until the bottom)
                sub_img1 = img[split_points[-2]:split_points[-1],:]
                sub_img2 = img[split_points[-1]:,:]
                adjusted_rect1 = adjust_rects(groups[i], 0,split_points[-2])
                adjusted_rect2 = adjust_rects(groups[i+1], 0,split_points[-1])
                results1 = process_image(sub_img1, adjusted_rect1, path_in, out_root, debug_root, suffix=f"{suffix}_{i+1}", return_bytes=return_bytes)
                results2 = process_image(sub_img2, adjusted_rect2, path_in, out_root, debug_root, suffix=f"{suffix}_{i+2}", return_bytes=return_bytes)
                all_results.extend(results1)
                all_results.extend(results2)

            else:
                # If we can't determine top/bottom or find a split, save for debug
                if not return_bytes:
                    save_debug_image(img, path_in, "3g")
        return all_results


    else:
        if not return_bytes:
            save_debug_image(img, path_in, "3g") # Default for >3 groups
        return []
        

def process_one(path_or_bytes, out_root: Path, debug_root: Path, ocr, regex, original_filename="image.png", return_bytes=False):
    import paddle
    
    if isinstance(path_or_bytes, (str, Path)):
        path_in = Path(path_or_bytes)
        img = cv2.imdecode(np.fromfile(str(path_in), dtype=np.uint8), cv2.IMREAD_GRAYSCALE)
    else:
        # It's bytes
        path_in = Path(original_filename) # Create a dummy path for filename conventions
        nparr = np.frombuffer(path_or_bytes, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_GRAYSCALE)


    if img is None:
        return False, "read_error", [], []

    if img.shape[0] == 0 or img.shape[1] == 0:
        return False, "invalid_image", [], []

    try:
        img_color = cv2.cvtColor(img, cv2.COLOR_GRAY2RGB) 
        result = ocr.predict(img_color)[0]
    except Exception as e:
        if "PreconditionNotMetError" in str(e):
            print(f"Caught PaddleOCR error: {e}. Re-initializing model and retrying...")
            import IP.Patents_Bulk.patent_image_spliter as splitter_mod
            splitter_mod._PADDLE_OCR_SINGLETON = None
            ocr = splitter_mod.get_paddle_ocr()
            result = ocr.predict(img_color)[0]
        else:
            raise

    rects = []
    for line_txt, box in zip(result["rec_texts"], result["dt_polys"]):
        if line_txt and regex.match(line_txt):
            rects.append(np.array(box).astype(int))

    if not rects:
        # If no text is found, but we need to return bytes, process the original image
        if return_bytes:
            return True, "ok_no_match", [img], []
        else:
            if not return_bytes:
                save_debug_image(img, path_in, "0")
            return True, "ok_no_match", [], []

    # Draw rectangles on a copy of the image for saving
    # img_with_rects = img.copy()
    # for rect in rects:
        # poly = np.array(rect, dtype=np.int32).reshape((-1, 1, 2))
        # cv2.polylines(img_with_rects, [poly], isClosed=True, color=(0, 255, 0), thickness=2)

    # Save the image with rectangles
    # if os.path.exists(RECT_DIR):
    #     rect_path = Path(RECT_DIR) / path_in.name
    #     if path_in.suffix.lower() in ['.tiff', '.tif']:
    #         save_tiff_image(img_with_rects, rect_path)
    #     else:
    #         cv2.imencode(path_in.suffix, img_with_rects)[1].tofile(str(rect_path))

    results = process_image(img, rects, path_in, out_root, debug_root, suffix="", return_bytes=return_bytes)
    
    if not results and return_bytes:
        # If processing failed but we need to return bytes, return the original image
        return True, "ok_no_split", [img], rects
    
    return True, "ok", results, rects # Debug images are the one with > 1 rect but only one output image

def process_worker(path_in, in_root, out_root, debug_root, regex):
    """Wrapper for multiprocessing; handles paths and errors."""
    ocr = get_paddle_ocr()

    try:
        ok, code, _, _ = process_one(
            path_or_bytes=path_in,
            out_root=out_root,
            debug_root=debug_root,
            ocr=ocr,
            regex=regex
        )
        return ok, code, path_in
    except Exception as e:
        print(f"Error processing {path_in}: {e}")
        return False, "other_error", path_in

def _init_worker():
    os.environ.setdefault("OMP_NUM_THREADS", "1")
    os.environ.setdefault("MKL_NUM_THREADS", "1")
    os.environ.setdefault("OPENBLAS_NUM_THREADS", "1")

def main():
    import paddle
    
    # --- Configuration ---
    # Script options
    JOBS = 4

    # --- Configuration ---
    INPUT_DIR = "./FilesAll"
    INPUT_DIR = r"D:\Win10User\Downloads\TestTiff"
    OUTPUT_DIR = "./FilesAllSplit"
    Path(OUTPUT_DIR).mkdir(exist_ok=True)

    print(f"Output directory: {Path(OUTPUT_DIR).resolve()}")
    # print(f"Rectangle directory: {Path(RECT_DIR).resolve()}")


    in_root = Path(INPUT_DIR).resolve()
    out_root = Path(OUTPUT_DIR).resolve()
    debug_root = Path(DEBUG_DIR).resolve()

    if not in_root.exists():
        print(f"Input path not found: {in_root}", file=sys.stderr)
        sys.exit(1)

    # Auto-detect GPU
    use_gpu = paddle.is_compiled_with_cuda()
    if not use_gpu:
        print("No NVIDIA GPU detected or paddlepaddle-gpu not installed. Falling back to CPU.")

    # Multiprocessing setup
    jobs = JOBS
    if use_gpu and jobs != 1:
        print(f"GPU detected: forcing single-process execution (was {jobs}).", file=sys.stderr)
        jobs = 1


    regex = re.compile(r'^fig', re.I)

    files = [p for p in in_root.rglob("*") if p.is_file() and valid_image_ext(p)]
    if not files:
        print("No image files found.", file=sys.stderr)
        sys.exit(2)

    stats = {"ok": 0, "ok_no_match": 0, "read_error": 0, "write_error": 0, "other_error": 0}
    
    worker_func = functools.partial(
        process_worker,
        in_root=in_root,
        out_root=out_root,
        debug_root=debug_root,
        regex=regex,
    )

    if jobs > 1:
        print(f"Processing with {jobs} parallel jobs...")
        with mp.Pool(processes=jobs, initializer=_init_worker) as pool:
            with tqdm(total=len(files), desc="Processing", unit="img") as pbar:
                for ok, code, path_in in pool.imap_unordered(worker_func, files):
                    stats[code] = stats.get(code, 0) + 1
                    if not ok:
                        tqdm.write(f"Error processing {path_in}: {code}", file=sys.stderr)
                    pbar.update(1)
    else:
        print("Processing with 1 job...")
        for p in tqdm(files, desc="Processing", unit="img"):
            ok, code, path_in = worker_func(p)
            stats[code] = stats.get(code, 0) + 1
            if not ok:
                tqdm.write(f"Error processing {path_in}: {code}", file=sys.stderr)


    print("\nDone.")
    print(f" Saved: {stats['ok']} (with matches), {stats['ok_no_match']} (no match, copied unchanged)")
    print(f" Read errors: {stats['read_error']}, Write errors: {stats['write_error']}, Other: {stats['other_error']}")


if __name__ == "__main__":
    main()
