"""
Test script for LegalOpinion backend functionality.

This script tests the basic functionality of the LegalOpinion backend
without requiring a full web server setup.
"""

import asyncio
import json
import os
import sys
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.append(str(Path(__file__).parent.parent))

from LegalOpinion.form_processor import LegalOpinionFormProcessor
from LegalOpinion.patent_utils import normalize_patent_number, validate_patent_number_format
from LegalOpinion.submission_workspace import SubmissionWorkspaceManager


def test_patent_utils():
    """Test patent utility functions."""
    print("Testing patent utility functions...")
    
    # Test patent number normalization
    test_cases = [
        ("USD1000051-S1", "D1000051"),
        ("USD0001234", "D1234"),
        ("US D 001234", "D1234"),
        ("D0640227", "D640227"),
        ("1000051", "1000051"),
        ("", "")
    ]
    
    for input_patent, expected in test_cases:
        result = normalize_patent_number(input_patent)
        status = "✅" if result == expected else "❌"
        print(f"  {status} {input_patent} -> {result} (expected: {expected})")
    
    # Test patent validation
    print("\nTesting patent validation...")
    validation_cases = [
        ("D640227", True),
        ("USD1000051", True),
        ("1234567", True),
        ("invalid", False),
        ("", False)
    ]
    
    for patent, should_be_valid in validation_cases:
        result = validate_patent_number_format(patent)
        is_valid = result["valid"]
        status = "✅" if is_valid == should_be_valid else "❌"
        print(f"  {status} {patent} -> valid: {is_valid} (expected: {should_be_valid})")


def test_workspace_manager():
    """Test submission workspace manager."""
    print("\nTesting submission workspace manager...")
    
    # Use a test directory
    test_base_path = Path(__file__).parent / "test_workspaces"
    
    try:
        manager = SubmissionWorkspaceManager(str(test_base_path))
        
        # Test workspace creation
        test_submission_id = "LO-TEST001"
        workspace_paths = manager.create_submission_workspace(test_submission_id)
        
        print(f"  ✅ Created workspace: {workspace_paths['workspace']}")
        
        # Test workspace existence check
        exists = manager.workspace_exists(test_submission_id)
        status = "✅" if exists else "❌"
        print(f"  {status} Workspace exists check: {exists}")
        
        # Test metadata operations
        test_metadata = {"test": "data", "timestamp": "2024-01-01"}
        metadata_path = manager.save_metadata(test_submission_id, "manifests", "test_metadata", test_metadata)
        print(f"  ✅ Saved metadata: {metadata_path}")
        
        loaded_metadata = manager.load_metadata(test_submission_id, "manifests", "test_metadata")
        metadata_match = loaded_metadata == test_metadata
        status = "✅" if metadata_match else "❌"
        print(f"  {status} Metadata round-trip: {metadata_match}")
        
        # Test file hashing
        test_file_path = Path(workspace_paths["workspace"]) / "README.txt"
        if test_file_path.exists():
            file_hash = manager.compute_file_hash(str(test_file_path))
            status = "✅" if file_hash else "❌"
            print(f"  {status} File hash computed: {file_hash[:16]}..." if file_hash else "  ❌ File hash failed")
        
        print("  ✅ Workspace manager tests completed")
        
    except Exception as e:
        print(f"  ❌ Workspace manager test failed: {e}")


async def test_form_processor():
    """Test the main form processor."""
    print("\nTesting form processor...")
    
    try:
        processor = LegalOpinionFormProcessor()
        
        # Test form validation
        test_form_data = {
            "storeName": "Test Store",
            "platform": "amazon",
            "patentNumber": "USD1000051",
            "asins": ["B123456789"],
            "clientPatents": [
                {
                    "patentNumber": "D640227",
                    "country": "US",
                    "date": "2011-06-21"
                }
            ]
        }
        
        validation_result = processor.validate_form_data(test_form_data)
        is_valid = validation_result["valid"]
        status = "✅" if is_valid else "❌"
        print(f"  {status} Form validation: {is_valid}")
        if not is_valid:
            print(f"    Errors: {validation_result['errors']}")
        
        # Test submission ID generation
        submission_id = processor.generate_submission_id()
        id_format_ok = submission_id.startswith("LO-") and len(submission_id) == 11
        status = "✅" if id_format_ok else "❌"
        print(f"  {status} Submission ID generation: {submission_id}")
        
        print("  ✅ Form processor tests completed")
        
    except Exception as e:
        print(f"  ❌ Form processor test failed: {e}")


async def test_full_processing():
    """Test full processing workflow (without actual database/API calls)."""
    print("\nTesting full processing workflow...")
    
    try:
        # Use a test directory
        test_base_path = Path(__file__).parent / "test_full_processing"
        processor = LegalOpinionFormProcessor(str(test_base_path))
        
        # Test form data
        test_form_data = {
            "storeName": "Test Store",
            "platform": "amazon",
            "patentNumber": "USD1000051",
            "productDescription": "Test product description for patent infringement analysis",
            "asins": ["B123456789"],
            "clientPatents": [
                {
                    "patentNumber": "D640227",
                    "country": "US",
                    "date": "2011-06-21"
                }
            ],
            "firstPublicDisplayDate": "2020-01-01",
            "firstSaleDate": "2020-02-01"
        }
        
        # Process submission (this will fail at database/API calls, but should create workspace)
        result = await processor.process_submission(test_form_data)
        
        workspace_created = result.get("workspace_created", False)
        status = "✅" if workspace_created else "❌"
        print(f"  {status} Workspace creation: {workspace_created}")
        
        submission_id = result.get("submission_id", "")
        id_ok = submission_id.startswith("LO-")
        status = "✅" if id_ok else "❌"
        print(f"  {status} Submission ID: {submission_id}")
        
        # Check if workspace exists
        if submission_id:
            status_info = processor.get_submission_status(submission_id)
            exists = status_info.get("exists", False)
            status = "✅" if exists else "❌"
            print(f"  {status} Submission status check: {exists}")
        
        print("  ✅ Full processing workflow test completed")
        print(f"    Note: Database/API operations expected to fail in test environment")
        
    except Exception as e:
        print(f"  ❌ Full processing test failed: {e}")


def cleanup_test_directories():
    """Clean up test directories."""
    print("\nCleaning up test directories...")
    
    test_dirs = [
        Path(__file__).parent / "test_workspaces",
        Path(__file__).parent / "test_full_processing"
    ]
    
    for test_dir in test_dirs:
        if test_dir.exists():
            import shutil
            try:
                shutil.rmtree(test_dir)
                print(f"  ✅ Cleaned up: {test_dir}")
            except Exception as e:
                print(f"  ⚠️ Could not clean up {test_dir}: {e}")


async def main():
    """Run all tests."""
    print("🧪 LegalOpinion Backend Test Suite")
    print("=" * 50)
    
    # Run tests
    test_patent_utils()
    test_workspace_manager()
    await test_form_processor()
    await test_full_processing()
    
    print("\n" + "=" * 50)
    print("🏁 Test suite completed!")
    print("\nNote: Some tests may show warnings or errors related to database")
    print("connections or API calls, which is expected in the test environment.")
    
    # Cleanup
    cleanup_test_directories()


if __name__ == "__main__":
    asyncio.run(main())
