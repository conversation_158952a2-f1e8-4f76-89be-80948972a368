"""
Prior Art Processor

Handles processing of prior art documents and files for legal opinion submissions.
Manages copying files from USPTO_Grants and other sources to the prior_art directory.
"""

import os
import shutil
from pathlib import Path
from typing import Dict, List, Any, Optional
from logdata import log_message

from .figure_manager import FigureManager
from .evidence_manager import EvidenceManager


class PriorArtProcessor:
    """
    Processes prior art documents and files for legal opinion submissions.
    
    Handles:
    - Copying prior art files from USPTO_Grants
    - Processing uploaded prior art files
    - Managing prior art evidence tracking
    """
    
    def __init__(self, submission_id: str, prior_art_dir: str, evidence_manager: EvidenceManager):
        """
        Initialize the prior art processor.
        
        Args:
            submission_id: Unique submission identifier
            prior_art_dir: Directory for prior art files
            evidence_manager: Evidence manager instance
        """
        self.submission_id = submission_id
        self.prior_art_dir = Path(prior_art_dir)
        self.evidence_manager = evidence_manager
        self.figure_manager = FigureManager()
        
        # Ensure prior art directory exists
        self.prior_art_dir.mkdir(parents=True, exist_ok=True)
    
    async def process_uploaded_prior_art(self, uploaded_files: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process uploaded prior art files.
        
        Args:
            uploaded_files: Dictionary of uploaded files
            
        Returns:
            Processing results
        """
        result = {
            "success": False,
            "saved_files": [],
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            # Process prior art files
            for key, uploaded_file in uploaded_files.items():
                if key.startswith("priorArtFiles_"):
                    # Save the file
                    saved_file_path = self.prior_art_dir / uploaded_file.filename
                    
                    with open(saved_file_path, 'wb') as f:
                        content = await uploaded_file.read()
                        f.write(content)
                    
                    result["saved_files"].append(str(saved_file_path))
                    
                    # Add to evidence manifest
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=saved_file_path,
                        category="prior_art",
                        provenance="client_upload",
                        metadata={
                            "file_type": "prior_art_document",
                            "original_filename": uploaded_file.filename,
                            "source": "client_upload"
                        }
                    ):
                        result["evidence_entries"] += 1
            
            result["success"] = True
            log_message(f"Processed {len(result['saved_files'])} prior art files", level='INFO')
            
        except Exception as e:
            log_message(f"Error processing prior art files: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result
    
    def copy_patent_figures_as_prior_art(self, patent_data_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Copy patent figures from USPTO_Grants as prior art.
        
        Args:
            patent_data_list: List of patent data dictionaries with figure information
            
        Returns:
            Processing results
        """
        result = {
            "success": False,
            "copied_patents": [],
            "total_figures_copied": 0,
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            for patent_data in patent_data_list:
                reg_no = patent_data.get('reg_no', '')
                folder = patent_data.get('folder', '')
                
                if not reg_no or not folder:
                    log_message(f"Skipping patent with missing data: reg_no={reg_no}, folder={folder}", level='WARNING')
                    continue
                
                # Create subdirectory for this patent's prior art
                patent_prior_art_dir = self.prior_art_dir / f"patent_{reg_no}"
                patent_prior_art_dir.mkdir(exist_ok=True)
                
                # Copy figures using figure manager
                figure_result = self.figure_manager.process_patent_figures(
                    patent_data=patent_data,
                    destination_dir=str(patent_prior_art_dir)
                )
                
                if figure_result.get("success", False):
                    copied_files = figure_result.get("copied_files", [])
                    
                    # Add each copied figure to evidence manifest
                    for figure_path in copied_files:
                        if self.evidence_manager.add_file_to_manifest(
                            file_path=figure_path,
                            category="prior_art",
                            provenance="USPTO_Grants",
                            metadata={
                                "file_type": "design_figure",
                                "patent_reg_no": reg_no,
                                "source": "USPTO_Grants",
                                "prior_art_type": "patent_figures"
                            }
                        ):
                            result["evidence_entries"] += 1
                    
                    result["copied_patents"].append({
                        "reg_no": reg_no,
                        "figures_copied": len(copied_files),
                        "destination_dir": str(patent_prior_art_dir)
                    })
                    
                    result["total_figures_copied"] += len(copied_files)
                    
                    log_message(f"Copied {len(copied_files)} figures for patent {reg_no} as prior art", level='INFO')
                else:
                    log_message(f"Failed to copy figures for patent {reg_no}: {figure_result.get('error', 'Unknown error')}", level='WARNING')
            
            result["success"] = True
            log_message(f"Copied figures from {len(result['copied_patents'])} patents as prior art", level='INFO')
            
        except Exception as e:
            log_message(f"Error copying patent figures as prior art: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result
    
    def copy_files_from_source(self, source_files: List[str], source_description: str = "external") -> Dict[str, Any]:
        """
        Copy files from external sources to prior art directory.
        
        Args:
            source_files: List of source file paths to copy
            source_description: Description of the source (for metadata)
            
        Returns:
            Processing results
        """
        result = {
            "success": False,
            "copied_files": [],
            "failed_files": [],
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            for source_path in source_files:
                source_file = Path(source_path)
                
                if not source_file.exists():
                    result["failed_files"].append(str(source_path))
                    log_message(f"Source file not found: {source_path}", level='WARNING')
                    continue
                
                # Determine destination filename
                destination_path = self.prior_art_dir / source_file.name
                
                # Handle filename conflicts
                counter = 1
                while destination_path.exists():
                    stem = source_file.stem
                    suffix = source_file.suffix
                    destination_path = self.prior_art_dir / f"{stem}_{counter}{suffix}"
                    counter += 1
                
                try:
                    # Copy the file
                    shutil.copy2(source_path, destination_path)
                    result["copied_files"].append(str(destination_path))
                    
                    # Add to evidence manifest
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=destination_path,
                        category="prior_art",
                        provenance=source_description,
                        metadata={
                            "file_type": "prior_art_document",
                            "original_path": str(source_path),
                            "source": source_description
                        }
                    ):
                        result["evidence_entries"] += 1
                    
                    log_message(f"Copied prior art file: {source_file.name}", level='DEBUG')
                    
                except Exception as e:
                    result["failed_files"].append(str(source_path))
                    log_message(f"Failed to copy file {source_path}: {e}", level='ERROR')
            
            result["success"] = len(result["copied_files"]) > 0 or len(source_files) == 0
            
            log_message(f"Copied {len(result['copied_files'])} prior art files from {source_description}", level='INFO')
            
        except Exception as e:
            log_message(f"Error copying files from {source_description}: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result
    
    def process_form_prior_art(self, prior_art_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Process prior art information from form data.
        
        Args:
            prior_art_data: List of prior art entries from form
            
        Returns:
            Processing results
        """
        result = {
            "success": False,
            "processed_entries": [],
            "evidence_entries": 0,
            "error": None
        }
        
        try:
            for i, prior_art_entry in enumerate(prior_art_data):
                entry_result = {
                    "index": i,
                    "description": prior_art_entry.get("description", ""),
                    "source": prior_art_entry.get("source", ""),
                    "date": prior_art_entry.get("date", ""),
                    "processed": False
                }
                
                # Save prior art metadata
                metadata_filename = f"prior_art_entry_{i}"
                metadata_path = self.prior_art_dir / f"{metadata_filename}.json"
                
                try:
                    import json
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(prior_art_entry, f, indent=2, default=str)
                    
                    # Add metadata to evidence manifest
                    if self.evidence_manager.add_file_to_manifest(
                        file_path=metadata_path,
                        category="prior_art",
                        provenance="form_data",
                        metadata={
                            "file_type": "prior_art_metadata",
                            "entry_index": i,
                            "source": "form_data"
                        }
                    ):
                        result["evidence_entries"] += 1
                    
                    entry_result["processed"] = True
                    entry_result["metadata_path"] = str(metadata_path)
                    
                except Exception as e:
                    log_message(f"Error saving prior art entry {i}: {e}", level='ERROR')
                    entry_result["error"] = str(e)
                
                result["processed_entries"].append(entry_result)
            
            result["success"] = True
            log_message(f"Processed {len(prior_art_data)} prior art entries from form", level='INFO')
            
        except Exception as e:
            log_message(f"Error processing form prior art data: {e}", level='ERROR')
            result["error"] = str(e)
        
        return result
