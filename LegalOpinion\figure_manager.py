"""
Figure Manager

Handles locating and copying design figure files from USPTO_Grants store
for legal opinion processing.
"""

import os
import shutil
from pathlib import Path
from typing import List, Dict, Any, Optional
from logdata import log_message

# Import existing patent parser utilities
from IP.Patents_Bulk.patent_parser import _get_or_create_patent_img_destination_directory
from Common.Constants import local_ip_folder


class FigureManager:
    """
    Manages design figure files for legal opinion processing.
    
    Locates and copies figure files from the USPTO_Grants store based on
    database metadata including folder name and fig_files list.
    """
    
    def __init__(self, source_base_dir: Optional[str] = None):
        """
        Initialize the figure manager.
        
        Args:
            source_base_dir: Base directory for USPTO_Grants extracted files.
                           Defaults to {local_ip_folder}/Patents/USPTO_Grants/Extracted/
        """
        if source_base_dir is None:
            self.source_base_dir = os.path.join(local_ip_folder, "Patents", "USPTO_Grants", "Extracted")
        else:
            self.source_base_dir = source_base_dir
        
        log_message(f"FigureManager initialized with source: {self.source_base_dir}", level='INFO')
    
    def get_source_figure_directory(self, reg_no: str, folder: str) -> Optional[Path]:
        """
        Get the source directory path for patent figures using existing utility.
        
        Args:
            reg_no: Patent registration number (e.g., "D640227")
            folder: Folder name from database (e.g., "USD0640227-20110621")
            
        Returns:
            Path to source figure directory or None if not found
        """
        try:
            # Use existing utility to generate the folder path
            source_dir = _get_or_create_patent_img_destination_directory(
                patent_num=reg_no,
                base_dir=self.source_base_dir,
                image_folder_name=folder
            )
            
            if source_dir and source_dir.exists():
                log_message(f"Found source figure directory: {source_dir}", level='INFO')
                return source_dir
            else:
                log_message(f"Source figure directory not found: {source_dir}", level='WARNING')
                return None
                
        except Exception as e:
            log_message(f"Error getting source figure directory for {reg_no}: {e}", level='ERROR')
            return None
    
    def list_available_figures(self, reg_no: str, folder: str, fig_files: Optional[List[str]] = None) -> List[str]:
        """
        List available figure files for a patent.
        
        Args:
            reg_no: Patent registration number
            folder: Folder name from database
            fig_files: List of figure filenames from database (optional)
            
        Returns:
            List of available figure file paths
        """
        available_figures = []
        
        try:
            source_dir = self.get_source_figure_directory(reg_no, folder)
            if not source_dir:
                return available_figures
            
            if fig_files:
                # Check for specific files listed in database
                for fig_file in fig_files:
                    fig_path = source_dir / fig_file
                    if fig_path.exists():
                        available_figures.append(str(fig_path))
                    else:
                        log_message(f"Figure file not found: {fig_path}", level='WARNING')
            else:
                # List all image files in the directory
                image_extensions = {'.tiff', '.tif', '.png', '.jpg', '.jpeg', '.gif'}
                for file_path in source_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in image_extensions:
                        available_figures.append(str(file_path))
            
            log_message(f"Found {len(available_figures)} figure files for {reg_no}", level='INFO')
            
        except Exception as e:
            log_message(f"Error listing figures for {reg_no}: {e}", level='ERROR')
        
        return available_figures
    
    def copy_figures_to_destination(self, reg_no: str, folder: str, fig_files: Optional[List[str]], 
                                  destination_dir: str) -> Dict[str, Any]:
        """
        Copy figure files from source to destination directory.
        
        Args:
            reg_no: Patent registration number
            folder: Folder name from database
            fig_files: List of figure filenames from database
            destination_dir: Destination directory to copy figures
            
        Returns:
            Dictionary with copy results:
            {
                "success": bool,
                "copied_files": List[str],
                "failed_files": List[str],
                "destination_dir": str
            }
        """
        result = {
            "success": False,
            "copied_files": [],
            "failed_files": [],
            "destination_dir": destination_dir
        }
        
        try:
            # Create destination directory
            os.makedirs(destination_dir, exist_ok=True)
            
            # Get list of available figures
            available_figures = self.list_available_figures(reg_no, folder, fig_files)
            
            if not available_figures:
                log_message(f"No figure files found for {reg_no}", level='WARNING')
                result["success"] = True  # Not an error, just no files
                return result
            
            # Copy each figure file
            for source_path in available_figures:
                try:
                    source_file = Path(source_path)
                    destination_path = os.path.join(destination_dir, source_file.name)
                    
                    # Copy the file
                    shutil.copy2(source_path, destination_path)
                    result["copied_files"].append(destination_path)
                    
                    log_message(f"Copied figure: {source_file.name} -> {destination_path}", level='DEBUG')
                    
                except Exception as e:
                    log_message(f"Failed to copy figure {source_path}: {e}", level='ERROR')
                    result["failed_files"].append(source_path)
            
            result["success"] = len(result["copied_files"]) > 0 or len(available_figures) == 0
            
            log_message(f"Copied {len(result['copied_files'])} figures for {reg_no} to {destination_dir}", level='INFO')
            
        except Exception as e:
            log_message(f"Error copying figures for {reg_no}: {e}", level='ERROR')
            result["success"] = False
        
        return result
    
    def process_patent_figures(self, patent_data: Dict[str, Any], destination_dir: str) -> Dict[str, Any]:
        """
        Process figures for a patent based on database metadata.
        
        Args:
            patent_data: Patent data dictionary from database containing:
                        - reg_no: Patent registration number
                        - folder: Folder name
                        - fig_files: List or string of figure filenames
            destination_dir: Destination directory for figures
            
        Returns:
            Dictionary with processing results
        """
        reg_no = patent_data.get('reg_no', '')
        folder = patent_data.get('folder', '')
        fig_files_raw = patent_data.get('fig_files', '')
        
        if not reg_no or not folder:
            log_message(f"Missing required patent data: reg_no={reg_no}, folder={folder}", level='ERROR')
            return {
                "success": False,
                "error": "Missing required patent data (reg_no or folder)",
                "copied_files": [],
                "failed_files": [],
                "destination_dir": destination_dir
            }
        
        # Parse fig_files - it might be a string representation of a set/list
        fig_files = self._parse_fig_files(fig_files_raw)
        
        log_message(f"Processing figures for patent {reg_no} from folder {folder}", level='INFO')
        
        return self.copy_figures_to_destination(reg_no, folder, fig_files, destination_dir)
    
    def _parse_fig_files(self, fig_files_raw: Any) -> Optional[List[str]]:
        """
        Parse fig_files from database which might be in various formats.
        
        Args:
            fig_files_raw: Raw fig_files data from database
            
        Returns:
            List of figure filenames or None
        """
        if not fig_files_raw:
            return None
        
        try:
            # If it's already a list
            if isinstance(fig_files_raw, list):
                return fig_files_raw
            
            # If it's a string representation of a set/list
            if isinstance(fig_files_raw, str):
                # Remove set/list brackets and split by comma
                cleaned = fig_files_raw.strip('{}[]')
                if cleaned:
                    # Split by comma and clean each filename
                    fig_files = [f.strip().strip('"\'') for f in cleaned.split(',')]
                    return [f for f in fig_files if f]  # Remove empty strings
            
            # Try to convert to string and parse
            fig_files_str = str(fig_files_raw)
            cleaned = fig_files_str.strip('{}[]')
            if cleaned:
                fig_files = [f.strip().strip('"\'') for f in cleaned.split(',')]
                return [f for f in fig_files if f]
            
        except Exception as e:
            log_message(f"Error parsing fig_files: {fig_files_raw}, error: {e}", level='WARNING')
        
        return None
    
    def get_figure_info(self, reg_no: str, folder: str, fig_files: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Get information about available figures without copying them.
        
        Args:
            reg_no: Patent registration number
            folder: Folder name from database
            fig_files: List of figure filenames from database
            
        Returns:
            Dictionary with figure information
        """
        try:
            source_dir = self.get_source_figure_directory(reg_no, folder)
            available_figures = self.list_available_figures(reg_no, folder, fig_files)
            
            return {
                "reg_no": reg_no,
                "folder": folder,
                "source_directory": str(source_dir) if source_dir else None,
                "source_exists": source_dir.exists() if source_dir else False,
                "expected_files": fig_files or [],
                "available_files": [Path(f).name for f in available_figures],
                "available_count": len(available_figures),
                "missing_files": []
            }
            
        except Exception as e:
            log_message(f"Error getting figure info for {reg_no}: {e}", level='ERROR')
            return {
                "reg_no": reg_no,
                "folder": folder,
                "error": str(e),
                "source_directory": None,
                "source_exists": False,
                "expected_files": fig_files or [],
                "available_files": [],
                "available_count": 0,
                "missing_files": []
            }
