import asyncio
import logging
from datetime import datetime
import traceback
from Scheduler_ARQ.utils.email_utils import send_error_notification_email, send_maijiazhichi_report_email

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def trademark_uspto_daily_task(ctx):
    """
    ARQ task to run the USPTO trademark daily scraper.
    """
    try:
        logger.info("Starting USPTO trademark daily scraper task.")
        
        from IP.Trademarks_Bulk.get_bulk_trademarks import process_bulk_trademarks
        # Make the call async-compatible
        stats = await asyncio.get_event_loop().run_in_executor(
            None, process_bulk_trademarks, "daily", 1
        )

        logger.info("USPTO trademark daily scraper task completed successfully.")
        return {
            'status': 'completed',
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'message': 'USPTO trademark daily scraper task completed successfully'
        }

    except Exception as exc:
        error_msg = f"USPTO trademark daily scraper task failed: {str(exc)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())

        await asyncio.get_event_loop().run_in_executor(
            None, send_error_notification_email, str(exc), "USPTO Trademark Daily Scraper"
        )
        raise exc

async def maijiazhichi_scraper_task(ctx):
    """
    ARQ task to run the Maijiazhichi scraper.
    """
    try:
        logger.info("Starting Maijiazhichi scraper task.")
        
        from Scraper.ChineseWebsite_OutOfTheFlow.maijiazhichi import get_new_case_from_maijiazhichi
        stats = await get_new_case_from_maijiazhichi()
        
        if stats:
            await asyncio.get_event_loop().run_in_executor(
                None, send_maijiazhichi_report_email, stats
            )

        logger.info("Maijiazhichi scraper task completed successfully.")
        return {
            'status': 'completed',
            'timestamp': datetime.now().isoformat(),
            'stats': stats,
            'message': 'Maijiazhichi scraper task completed successfully'
        }

    except Exception as exc:
        error_msg = f"Maijiazhichi scraper task failed: {str(exc)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        
        await asyncio.get_event_loop().run_in_executor(
            None, send_error_notification_email, str(exc), "Maijiazhichi Scraper"
        )
        raise exc