"""
Automated Prior Art Retrieval System

This module implements the comprehensive prior art retrieval system as specified:
- LLM keyword generation from patent titles and visual descriptions
- Text queries using BGE embeddings in Qdrant Patent_Title_bge_small collection
- Image queries using SigLIP embeddings in Qdrant IP_Assets_Optimized collection
- 2-stage scoring with normalization, article matching, age bonus, and fusion
- Deduplication using DBSCAN clustering
- LLM-based persuasion scoring for final ranking
"""

import os
import json
import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from pathlib import Path
import logging

# Database and vector store imports
from qdrant_client import QdrantClient
from qdrant_client.http import models
from sentence_transformers import SentenceTransformer
from sklearn.cluster import DBSCAN

# Internal imports
from logdata import log_message
from AI.GC_VertexAI import vertex_genai_multi_async
from AI.LLM_shared import get_json
from Common.Constants import TEXT_MODEL_FREE
from IP.Trademarks_Bulk.trademark_db import get_db_connection
from LegalOpinion.Curver_check import CurverAnalyzer


class AutomatedPriorArtRetrieval:
    """
    Automated Prior Art Retrieval System implementing the Egyptian Goddess standard
    for design patent prior art analysis.
    """
    
    def __init__(self, submission_id: str, prior_art_dir: str):
        """
        Initialize the automated prior art retrieval system.
        
        Args:
            submission_id: Unique submission identifier
            prior_art_dir: Directory for prior art files
        """
        self.submission_id = submission_id
        self.prior_art_dir = Path(prior_art_dir)
        self.prior_art_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Qdrant client
        self.qdrant_client = QdrantClient(
            url=os.getenv("QDRANT_URL", "https://vectorstore1.maidalv.com:6333"),
            api_key=os.getenv("QDRANT_API_KEY"),
            timeout=30,
            https=True
        )
        
        # Initialize BGE model for text embeddings
        self.bge_model = None
        self._load_bge_model()
        
        # Initialize Curver analyzer for article matching
        self.curver_analyzer = CurverAnalyzer()
        
        # Configuration parameters
        self.config = {
            "top_k_text_per_query": 20,
            "top_k_img_per_view": 40,
            "text_pool_cap": 60,
            "image_pool_cap": 80,
            "final_shortlist_size": 20,
            "close_prior_art_threshold": 80,
            "close_prior_art_max": 3
        }
    
    def _load_bge_model(self):
        """Load the BGE model for text embeddings."""
        try:
            self.bge_model = SentenceTransformer('BAAI/bge-small-en-v1.5')
            log_message("BGE model loaded successfully", level='INFO')
        except Exception as e:
            log_message(f"Error loading BGE model: {e}", level='ERROR')
            raise
    
    async def generate_search_keywords(self, patent_title: str, client_title: str = "", 
                                     visual_description: str = "") -> List[str]:
        """
        Generate 3 short keyword queries using LLM.
        
        Args:
            patent_title: Asserted patent title
            client_title: Client patent title (optional)
            visual_description: Visual description from Curver check
            
        Returns:
            List of 3 keyword queries
        """
        try:
            prompt = f"""Given the following patent information, generate exactly 3 short keyword queries (no quotes, 2-4 words each) that a patent examiner would use to find similar prior designs for the article of manufacture.

ASSERTED_TITLE: "{patent_title}"
CLIENT_TITLE: "{client_title}"
Visual description: {visual_description}

Focus on the article of manufacture itself, not the ornamental features. Think about what category of product this is and what similar products might exist.

Format: term1 | term2 | term3

Examples:
- For a storage basket: "storage basket | container basket | woven basket"
- For a phone case: "phone case | mobile cover | device protector"
- For a chair: "chair design | seating furniture | ergonomic chair"

Return only the three terms separated by " | " with no additional text."""

            prompt_list = [("text", prompt)]
            
            response = await vertex_genai_multi_async(
                data_list=prompt_list,
                model_name=TEXT_MODEL_FREE,
                useVertexAI=True
            )
            
            if response and len(response) > 0:
                response_text = response[0].strip()
                
                # Parse the response to extract keywords
                if "|" in response_text:
                    keywords = [term.strip() for term in response_text.split("|")]
                    keywords = [kw for kw in keywords if kw]  # Remove empty strings
                    
                    if len(keywords) >= 3:
                        return keywords[:3]
                    else:
                        # Fallback: pad with variations of the patent title
                        while len(keywords) < 3:
                            keywords.append(patent_title.lower())
                        return keywords[:3]
                else:
                    # Fallback: use patent title variations
                    return [patent_title.lower(), patent_title.lower(), patent_title.lower()]
            
            # Ultimate fallback
            return [patent_title.lower(), patent_title.lower(), patent_title.lower()]
            
        except Exception as e:
            log_message(f"Error generating search keywords: {e}", level='ERROR')
            # Fallback to patent title
            return [patent_title.lower(), patent_title.lower(), patent_title.lower()]
    
    async def perform_text_queries(self, keywords: List[str]) -> Dict[str, Any]:
        """
        Perform semantic text queries using BGE embeddings in Patent_Title_bge_small collection.
        
        Args:
            keywords: List of keyword queries
            
        Returns:
            Dictionary with text query results
        """
        try:
            text_results = {}
            all_candidates = {}
            
            for i, keyword in enumerate(keywords):
                log_message(f"Performing text query {i+1}: {keyword}", level='INFO')
                
                # Generate embedding for the keyword
                keyword_embedding = self.bge_model.encode(keyword, normalize_embeddings=True)
                
                # Query Qdrant
                search_results = self.qdrant_client.search(
                    collection_name="Patent_Title_bge_small",
                    query_vector=keyword_embedding.tolist(),
                    limit=self.config["top_k_text_per_query"],
                    with_payload=True
                )
                
                query_results = []
                for result in search_results:
                    reg_no = result.payload.get("reg_no")
                    if reg_no:
                        candidate_data = {
                            "reg_no": reg_no,
                            "score": float(result.score),
                            "source": "text_keyword",
                            "query": keyword,
                            "query_index": i
                        }
                        query_results.append(candidate_data)
                        
                        # Keep best score per reg_no across all queries
                        if reg_no not in all_candidates or candidate_data["score"] > all_candidates[reg_no]["score"]:
                            all_candidates[reg_no] = candidate_data
                
                text_results[f"query_{i}"] = query_results
            
            # Cap text pool and sort by score
            sorted_candidates = sorted(all_candidates.values(), key=lambda x: x["score"], reverse=True)
            text_pool = sorted_candidates[:self.config["text_pool_cap"]]
            
            log_message(f"Text queries completed: {len(text_pool)} candidates in pool", level='INFO')
            
            return {
                "success": True,
                "individual_queries": text_results,
                "text_pool": text_pool,
                "total_candidates": len(text_pool)
            }
            
        except Exception as e:
            log_message(f"Error performing text queries: {e}", level='ERROR')
            return {
                "success": False,
                "error": str(e),
                "text_pool": []
            }
    
    async def perform_image_queries(self, product_images: List[str]) -> Dict[str, Any]:
        """
        Perform image similarity queries using SigLIP embeddings in IP_Assets_Optimized collection.
        
        Args:
            product_images: List of product image file paths
            
        Returns:
            Dictionary with image query results
        """
        try:
            # Import SigLIP model here to avoid loading if not needed
            from Check.RAG.siglip_model import SiglipModel
            
            # Initialize SigLIP model
            siglip_config = {
                "vector_size": 1024,
                "model_name_or_path": "google/siglip2-large-patch16-512"
            }
            siglip_model = SiglipModel(model_id="siglip2_large_patch16_512", config=siglip_config)
            siglip_model.load()
            
            image_results = {}
            all_candidates = {}
            
            for i, image_path in enumerate(product_images):
                if not os.path.exists(image_path):
                    log_message(f"Image not found: {image_path}", level='WARNING')
                    continue
                
                log_message(f"Performing image query {i+1}: {os.path.basename(image_path)}", level='INFO')
                
                # Generate SigLIP embedding for the image
                image_embedding = siglip_model.compute_features(
                    data_list=[image_path], 
                    data_type="image"
                )[0].tolist()
                
                # Query Qdrant IP_Assets_Optimized collection
                search_results = self.qdrant_client.query_points(
                    collection_name="IP_Assets_Optimized",
                    query=image_embedding,
                    using="siglip_vector",
                    limit=self.config["top_k_img_per_view"],
                    query_filter=models.Filter(
                        must=[models.FieldCondition(
                            key="ip_type", 
                            match=models.MatchValue(value="Patent")
                        )]
                    ),
                    with_payload=True,
                    with_vectors=False
                )
                
                query_results = []
                for result in search_results.points:
                    reg_no = result.payload.get("reg_no")
                    if reg_no:
                        candidate_data = {
                            "reg_no": reg_no,
                            "score": float(result.score),
                            "source": "image_sim",
                            "query_image": os.path.basename(image_path),
                            "query_index": i
                        }
                        query_results.append(candidate_data)
                        
                        # Keep best score per reg_no across all images (max-pool)
                        if reg_no not in all_candidates or candidate_data["score"] > all_candidates[reg_no]["score"]:
                            all_candidates[reg_no] = candidate_data
                
                image_results[f"image_{i}"] = query_results
            
            # Cap image pool and sort by score
            sorted_candidates = sorted(all_candidates.values(), key=lambda x: x["score"], reverse=True)
            image_pool = sorted_candidates[:self.config["image_pool_cap"]]
            
            log_message(f"Image queries completed: {len(image_pool)} candidates in pool", level='INFO')
            
            return {
                "success": True,
                "individual_queries": image_results,
                "image_pool": image_pool,
                "total_candidates": len(image_pool)
            }
            
        except Exception as e:
            log_message(f"Error performing image queries: {e}", level='ERROR')
            return {
                "success": False,
                "error": str(e),
                "image_pool": []
            }

    async def create_union_and_cross_score(self, text_pool: List[Dict], image_pool: List[Dict]) -> List[Dict]:
        """
        Create union of text and image pools, then cross-score missing modalities.

        Args:
            text_pool: Candidates from text queries
            image_pool: Candidates from image queries

        Returns:
            List of candidates with both text and image scores
        """
        try:
            # Create union by reg_no
            union_dict = {}

            # Add text pool candidates
            for candidate in text_pool:
                reg_no = candidate["reg_no"]
                union_dict[reg_no] = {
                    "reg_no": reg_no,
                    "text_score": candidate["score"],
                    "text_source": candidate["source"],
                    "image_score": 0.0,
                    "image_source": None,
                    "text_missing": False,
                    "image_missing": True
                }

            # Add image pool candidates
            for candidate in image_pool:
                reg_no = candidate["reg_no"]
                if reg_no in union_dict:
                    # Update existing entry with image score
                    union_dict[reg_no]["image_score"] = candidate["score"]
                    union_dict[reg_no]["image_source"] = candidate["source"]
                    union_dict[reg_no]["image_missing"] = False
                else:
                    # New entry from image only
                    union_dict[reg_no] = {
                        "reg_no": reg_no,
                        "text_score": 0.0,
                        "text_source": None,
                        "image_score": candidate["score"],
                        "image_source": candidate["source"],
                        "text_missing": True,
                        "image_missing": False
                    }

            union_candidates = list(union_dict.values())
            log_message(f"Union created: {len(union_candidates)} unique candidates", level='INFO')

            # TODO: Implement cross-scoring for missing modalities
            # This would involve querying the missing modality for each candidate
            # For now, we'll proceed with the union as-is

            return union_candidates

        except Exception as e:
            log_message(f"Error creating union and cross-scoring: {e}", level='ERROR')
            return []

    async def fetch_patent_data_for_candidates(self, candidates: List[Dict]) -> List[Dict]:
        """
        Fetch patent data from database for all candidates.

        Args:
            candidates: List of candidate dictionaries with reg_no

        Returns:
            List of candidates enriched with patent data
        """
        try:
            enriched_candidates = []

            conn = get_db_connection()
            if not conn:
                log_message("Failed to get database connection", level='ERROR')
                return candidates

            cursor = conn.cursor()

            for candidate in candidates:
                reg_no = candidate["reg_no"]

                try:
                    # Query patent data
                    query = """
                    SELECT id, reg_no, patent_title, date_published, loc_code, uspc_class, uspc_subclass,
                           folder, fig_files, abstract, inventors, assignee
                    FROM public.patents
                    WHERE reg_no = %s LIMIT 1;
                    """
                    cursor.execute(query, (reg_no,))
                    result = cursor.fetchone()

                    if result:
                        columns = [desc[0] for desc in cursor.description]
                        patent_data = dict(zip(columns, result))

                        # Enrich candidate with patent data
                        enriched_candidate = candidate.copy()
                        enriched_candidate["patent_data"] = patent_data
                        enriched_candidate["title"] = patent_data.get("patent_title", "")
                        enriched_candidate["date_published"] = patent_data.get("date_published")
                        enriched_candidate["loc_code"] = patent_data.get("loc_code", "")
                        enriched_candidate["uspc_class"] = patent_data.get("uspc_class", "")
                        enriched_candidate["folder"] = patent_data.get("folder", "")
                        enriched_candidate["fig_files"] = patent_data.get("fig_files", [])

                        enriched_candidates.append(enriched_candidate)
                    else:
                        log_message(f"No patent data found for reg_no: {reg_no}", level='WARNING')
                        # Still add candidate but mark as missing data
                        enriched_candidate = candidate.copy()
                        enriched_candidate["patent_data"] = None
                        enriched_candidate["title"] = ""
                        enriched_candidate["date_published"] = None
                        enriched_candidates.append(enriched_candidate)

                except Exception as e:
                    log_message(f"Error fetching data for {reg_no}: {e}", level='ERROR')
                    # Add candidate without enrichment
                    enriched_candidates.append(candidate)

            conn.close()
            log_message(f"Enriched {len(enriched_candidates)} candidates with patent data", level='INFO')
            return enriched_candidates

        except Exception as e:
            log_message(f"Error fetching patent data: {e}", level='ERROR')
            return candidates

    def normalize_scores(self, candidates: List[Dict]) -> List[Dict]:
        """
        Normalize scores per modality using min-max scaling.

        Args:
            candidates: List of candidates with scores

        Returns:
            List of candidates with normalized scores
        """
        try:
            if not candidates:
                return candidates

            # Extract scores for normalization
            text_scores = [c["text_score"] for c in candidates if c["text_score"] > 0]
            image_scores = [c["image_score"] for c in candidates if c["image_score"] > 0]

            # Calculate min-max for each modality
            text_min = min(text_scores) if text_scores else 0
            text_max = max(text_scores) if text_scores else 1
            text_range = text_max - text_min + 1e-6

            image_min = min(image_scores) if image_scores else 0
            image_max = max(image_scores) if image_scores else 1
            image_range = image_max - image_min + 1e-6

            # Normalize scores
            for candidate in candidates:
                if candidate["text_score"] > 0:
                    candidate["text_score_norm"] = (candidate["text_score"] - text_min) / text_range
                else:
                    candidate["text_score_norm"] = 0.0

                if candidate["image_score"] > 0:
                    candidate["image_score_norm"] = (candidate["image_score"] - image_min) / image_range
                else:
                    candidate["image_score_norm"] = 0.0

            log_message(f"Normalized scores for {len(candidates)} candidates", level='INFO')
            return candidates

        except Exception as e:
            log_message(f"Error normalizing scores: {e}", level='ERROR')
            return candidates

    async def calculate_article_match_bonus(self, asserted_patent_data: Dict, candidates: List[Dict]) -> List[Dict]:
        """
        Calculate article match bonus using Curver legal standard.

        Args:
            asserted_patent_data: Asserted patent data
            candidates: List of candidates to score

        Returns:
            List of candidates with article match bonus
        """
        try:
            asserted_title = asserted_patent_data.get("patent_title", "")
            asserted_loc = asserted_patent_data.get("loc_code", "")
            asserted_uspc = asserted_patent_data.get("uspc_class", "")

            for candidate in candidates:
                candidate_data = candidate.get("patent_data", {})
                if not candidate_data:
                    candidate["article_bonus"] = 0.0
                    continue

                candidate_title = candidate_data.get("patent_title", "")
                candidate_loc = candidate_data.get("loc_code", "")
                candidate_uspc = candidate_data.get("uspc_class", "")

                # Create prompt for LLM article comparison
                prompt = f"""Compare these two design patents and score their article similarity based on the Curver legal standard. The Curver standard requires that the accused product be the same article of manufacture or a very close variant serving the same functional role.

ASSERTED PATENT:
- Title: "{asserted_title}"
- LOC Code: "{asserted_loc}"
- USPC Class: "{asserted_uspc}"

CANDIDATE PATENT:
- Title: "{candidate_title}"
- LOC Code: "{candidate_loc}"
- USPC Class: "{candidate_uspc}"

Score the article similarity on a scale of 0-10 where:
- 10: Same article of manufacture (e.g., both are "storage baskets")
- 7-9: Close variant serving same functional role (e.g., "storage basket" vs "container basket")
- 4-6: Related but different functional role (e.g., "storage basket" vs "decorative basket")
- 1-3: Different articles but same general category (e.g., "basket" vs "bowl")
- 0: Completely different articles (e.g., "basket" vs "chair")

Return only a single number between 0 and 10."""

                try:
                    prompt_list = [("text", prompt)]
                    response = await vertex_genai_multi_async(
                        data_list=prompt_list,
                        model_name=TEXT_MODEL_FREE,
                        useVertexAI=True
                    )

                    if response and len(response) > 0:
                        score_text = response[0].strip()
                        try:
                            score = float(score_text)
                            score = max(0.0, min(10.0, score))  # Clamp to 0-10
                            candidate["article_bonus"] = score / 10.0  # Normalize to 0-1
                        except ValueError:
                            log_message(f"Invalid article score response: {score_text}", level='WARNING')
                            candidate["article_bonus"] = 0.0
                    else:
                        candidate["article_bonus"] = 0.0

                except Exception as e:
                    log_message(f"Error calculating article bonus for {candidate['reg_no']}: {e}", level='ERROR')
                    candidate["article_bonus"] = 0.0

            log_message(f"Calculated article bonuses for {len(candidates)} candidates", level='INFO')
            return candidates

        except Exception as e:
            log_message(f"Error calculating article match bonuses: {e}", level='ERROR')
            return candidates

    def calculate_age_bonus(self, asserted_patent_data: Dict, candidates: List[Dict]) -> List[Dict]:
        """
        Calculate age bonus (older is better as prior art).

        Args:
            asserted_patent_data: Asserted patent data
            candidates: List of candidates to score

        Returns:
            List of candidates with age bonus
        """
        try:
            asserted_date = asserted_patent_data.get("date_published")
            if not asserted_date:
                log_message("No asserted patent date available for age bonus calculation", level='WARNING')
                for candidate in candidates:
                    candidate["age_bonus"] = 0.0
                return candidates

            # Convert to datetime if it's a string
            if isinstance(asserted_date, str):
                asserted_date = datetime.strptime(asserted_date, "%Y-%m-%d").date()

            for candidate in candidates:
                candidate_date = candidate.get("date_published")
                if not candidate_date:
                    candidate["age_bonus"] = 0.0
                    continue

                # Convert to datetime if it's a string
                if isinstance(candidate_date, str):
                    candidate_date = datetime.strptime(candidate_date, "%Y-%m-%d").date()

                # Calculate years delta
                days_delta = (asserted_date - candidate_date).days
                years_delta = days_delta / 365.0

                # Age bonus: min(1, max(0, 0.2 * years_delta))
                age_bonus = min(1.0, max(0.0, 0.2 * years_delta))
                candidate["age_bonus"] = age_bonus
                candidate["years_delta"] = years_delta

            log_message(f"Calculated age bonuses for {len(candidates)} candidates", level='INFO')
            return candidates

        except Exception as e:
            log_message(f"Error calculating age bonuses: {e}", level='ERROR')
            for candidate in candidates:
                candidate["age_bonus"] = 0.0
            return candidates

    def fuse_scores(self, candidates: List[Dict]) -> List[Dict]:
        """
        Fuse scores using weighted combination.

        Args:
            candidates: List of candidates with individual scores

        Returns:
            List of candidates with fused scores
        """
        try:
            for candidate in candidates:
                text_score = candidate.get("text_score_norm", 0.0)
                image_score = candidate.get("image_score_norm", 0.0)
                article_bonus = candidate.get("article_bonus", 0.0)
                age_bonus = candidate.get("age_bonus", 0.0)

                # Check if both modalities are present
                has_text = not candidate.get("text_missing", True)
                has_image = not candidate.get("image_missing", True)

                if has_text and has_image:
                    # Both modalities: 0.55*img + 0.35*text + 0.2*bonuses
                    bonuses = article_bonus + age_bonus
                    fused_score = 0.55 * image_score + 0.35 * text_score + 0.2 * bonuses
                elif has_text and not has_image:
                    # Text only: 0.7*text + 0.3*bonuses
                    bonuses = article_bonus + age_bonus
                    fused_score = 0.7 * text_score + 0.3 * bonuses
                elif has_image and not has_text:
                    # Image only: 0.7*image + 0.3*bonuses
                    bonuses = article_bonus + age_bonus
                    fused_score = 0.7 * image_score + 0.3 * bonuses
                else:
                    # Neither modality (shouldn't happen)
                    fused_score = article_bonus + age_bonus

                candidate["fused_score"] = fused_score
                candidate["bonuses_total"] = article_bonus + age_bonus

            log_message(f"Fused scores for {len(candidates)} candidates", level='INFO')
            return candidates

        except Exception as e:
            log_message(f"Error fusing scores: {e}", level='ERROR')
            return candidates

    async def deduplicate_and_diversify(self, candidates: List[Dict]) -> List[Dict]:
        """
        Deduplicate using DBSCAN clustering and diversify by article subtype.

        Args:
            candidates: List of candidates with fused scores

        Returns:
            List of deduplicated and diversified candidates
        """
        try:
            if len(candidates) <= 1:
                return candidates

            # TODO: Implement visual deduplication using DBSCAN
            # This would require fetching figure embeddings from IP_Assets_Optimized
            # For now, we'll implement article subtype diversification only

            # Group by LOC code for diversification
            loc_groups = {}
            for candidate in candidates:
                loc_code = candidate.get("loc_code", "unknown")
                if loc_code not in loc_groups:
                    loc_groups[loc_code] = []
                loc_groups[loc_code].append(candidate)

            # Cap per article subtype (max 3 per LOC code)
            diversified_candidates = []
            for loc_code, group in loc_groups.items():
                # Sort by fused score and take top 3
                group_sorted = sorted(group, key=lambda x: x.get("fused_score", 0), reverse=True)
                diversified_candidates.extend(group_sorted[:3])

            # Sort all candidates by fused score
            diversified_candidates.sort(key=lambda x: x.get("fused_score", 0), reverse=True)

            log_message(f"Deduplicated and diversified: {len(diversified_candidates)} candidates", level='INFO')
            return diversified_candidates

        except Exception as e:
            log_message(f"Error in deduplication and diversification: {e}", level='ERROR')
            return candidates

    async def llm_persuasion_scoring(self, candidates: List[Dict], asserted_patent_data: Dict) -> List[Dict]:
        """
        Score candidates using LLM for persuasion potential.

        Args:
            candidates: List of candidates to score
            asserted_patent_data: Asserted patent data for comparison

        Returns:
            List of candidates with LLM persuasion scores
        """
        try:
            asserted_title = asserted_patent_data.get("patent_title", "")

            for candidate in candidates:
                candidate_title = candidate.get("title", "")
                candidate_date = candidate.get("date_published", "")
                article_bonus = candidate.get("article_bonus", 0.0)
                age_bonus = candidate.get("age_bonus", 0.0)
                text_score = candidate.get("text_score_norm", 0.0)
                image_score = candidate.get("image_score_norm", 0.0)

                prompt = f"""Score this prior art patent for its persuasion potential in a design patent infringement case using the Egyptian Goddess standard.

ASSERTED PATENT: "{asserted_title}"
PRIOR ART CANDIDATE: "{candidate_title}"
Publication Date: {candidate_date}
Article Match Score: {article_bonus * 10:.1f}/10
Age Advantage: {age_bonus:.2f}
Text Similarity: {text_score:.2f}
Image Similarity: {image_score:.2f}

Score this candidate's persuasion potential (0-100 points) using these criteria:

1. Article match (Curver) – 25 pts
   - same article: +25
   - close variant (same functional role): +15
   - different: 0

2. Closeness to asserted design (visual & semantic) – 25 pts
   - Use image similarity primary, then text similarity
   - Close prior art gets +5 bonus

3. Age / earliest family date – 15 pts
   - +3 pts per full year older than asserted, capped at +15

4. Figure usefulness – 15 pts
   - multiple orthographic views +10
   - clarity estimate scaled 0–5

5. Diversity potential – 10 pts
   - Represents distinct ornamental motif vs other candidates

6. Simplicity – 10 pts
   - Easiest for reader to understand

Return only a single number between 0 and 100."""

                try:
                    prompt_list = [("text", prompt)]
                    response = await vertex_genai_multi_async(
                        data_list=prompt_list,
                        model_name=TEXT_MODEL_FREE,
                        useVertexAI=True
                    )

                    if response and len(response) > 0:
                        score_text = response[0].strip()
                        try:
                            score = float(score_text)
                            score = max(0.0, min(100.0, score))  # Clamp to 0-100
                            candidate["llm_persuasion_score"] = score
                        except ValueError:
                            log_message(f"Invalid LLM score response: {score_text}", level='WARNING')
                            candidate["llm_persuasion_score"] = 0.0
                    else:
                        candidate["llm_persuasion_score"] = 0.0

                except Exception as e:
                    log_message(f"Error calculating LLM score for {candidate['reg_no']}: {e}", level='ERROR')
                    candidate["llm_persuasion_score"] = 0.0

            log_message(f"Calculated LLM persuasion scores for {len(candidates)} candidates", level='INFO')
            return candidates

        except Exception as e:
            log_message(f"Error in LLM persuasion scoring: {e}", level='ERROR')
            return candidates

    def select_final_shortlist(self, candidates: List[Dict]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select final shortlist and identify close prior art.

        Args:
            candidates: List of scored candidates

        Returns:
            Tuple of (shortlist, close_prior_art)
        """
        try:
            # Sort by LLM persuasion score
            sorted_candidates = sorted(candidates, key=lambda x: x.get("llm_persuasion_score", 0), reverse=True)

            # Select top N for shortlist
            shortlist = sorted_candidates[:self.config["final_shortlist_size"]]

            # Identify close prior art based on threshold
            close_prior_art = []
            threshold = self.config["close_prior_art_threshold"]

            for candidate in shortlist:
                if candidate.get("llm_persuasion_score", 0) >= threshold:
                    close_prior_art.append(candidate)

            # Apply close prior art selection rules
            if len(close_prior_art) >= 3:
                # Keep 3 best if 3 are above 80
                close_prior_art = close_prior_art[:3]
            elif len(close_prior_art) >= 2:
                best_score = close_prior_art[0].get("llm_persuasion_score", 0)
                second_score = close_prior_art[1].get("llm_persuasion_score", 0)

                # Keep 2 if best is above 60 and second is less than 10% behind
                if best_score >= 60 and (best_score - second_score) / best_score <= 0.1:
                    close_prior_art = close_prior_art[:2]
                else:
                    close_prior_art = close_prior_art[:1]
            elif len(close_prior_art) >= 1:
                # Keep only 1 (the best one)
                close_prior_art = close_prior_art[:1]

            log_message(f"Selected shortlist: {len(shortlist)} candidates, close prior art: {len(close_prior_art)}", level='INFO')

            return shortlist, close_prior_art

        except Exception as e:
            log_message(f"Error selecting final shortlist: {e}", level='ERROR')
            return candidates[:self.config["final_shortlist_size"]], []

    async def fetch_and_save_prior_art_assets(self, candidates: List[Dict]) -> List[Dict]:
        """
        Fetch patent assets from USPTO_Grants and create metadata files.

        Args:
            candidates: List of final candidates

        Returns:
            List of candidates with asset paths
        """
        try:
            from LegalOpinion.figure_manager import FigureManager

            figure_manager = FigureManager()

            for candidate in candidates:
                reg_no = candidate["reg_no"]
                patent_data = candidate.get("patent_data")

                if not patent_data:
                    log_message(f"No patent data for {reg_no}, skipping asset fetch", level='WARNING')
                    continue

                # Create directory for this prior art patent
                patent_dir = self.prior_art_dir / reg_no
                patent_dir.mkdir(exist_ok=True)

                try:
                    # Copy figures using figure manager
                    figure_result = figure_manager.process_patent_figures(
                        patent_data=patent_data,
                        destination_dir=str(patent_dir)
                    )

                    candidate["figure_files"] = figure_result.get("copied_files", [])
                    candidate["asset_directory"] = str(patent_dir)

                    # Create metadata file
                    metadata = {
                        "reg_no": reg_no,
                        "source": candidate.get("text_source") or candidate.get("image_source", "unknown"),
                        "scores": {
                            "text_score": candidate.get("text_score", 0.0),
                            "image_score": candidate.get("image_score", 0.0),
                            "fused_score": candidate.get("fused_score", 0.0),
                            "llm_persuasion_score": candidate.get("llm_persuasion_score", 0.0),
                            "article_bonus": candidate.get("article_bonus", 0.0),
                            "age_bonus": candidate.get("age_bonus", 0.0)
                        },
                        "patent_data": {
                            "title": candidate.get("title", ""),
                            "date_published": str(candidate.get("date_published", "")),
                            "loc_code": candidate.get("loc_code", ""),
                            "uspc_class": candidate.get("uspc_class", ""),
                            "inventors": patent_data.get("inventors", ""),
                            "assignee": patent_data.get("assignee", "")
                        },
                        "retrieved_at": datetime.now().isoformat()
                    }

                    metadata_path = patent_dir / "meta.json"
                    with open(metadata_path, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, indent=2, default=str)

                    candidate["metadata_path"] = str(metadata_path)

                    log_message(f"Fetched assets for prior art patent: {reg_no}", level='INFO')

                except Exception as e:
                    log_message(f"Error fetching assets for {reg_no}: {e}", level='ERROR')
                    candidate["figure_files"] = []
                    candidate["asset_directory"] = str(patent_dir)

            return candidates

        except Exception as e:
            log_message(f"Error fetching prior art assets: {e}", level='ERROR')
            return candidates

    async def retrieve_automated_prior_art(self, asserted_patent_data: Dict,
                                         product_images: List[str],
                                         visual_description: str = "",
                                         client_title: str = "") -> Dict[str, Any]:
        """
        Main method to retrieve automated prior art using the complete pipeline.

        Args:
            asserted_patent_data: Asserted patent data from database
            product_images: List of product image file paths
            visual_description: Visual description from Curver check
            client_title: Client patent title (optional)

        Returns:
            Dictionary with complete prior art retrieval results
        """
        try:
            log_message(f"Starting automated prior art retrieval for submission: {self.submission_id}", level='INFO')

            result = {
                "success": False,
                "submission_id": self.submission_id,
                "stage_results": {},
                "shortlist": [],
                "close_prior_art": [],
                "total_candidates_found": 0,
                "processing_time": 0,
                "error": None
            }

            start_time = datetime.now()

            # Stage 1: Generate search keywords
            patent_title = asserted_patent_data.get("patent_title", "")
            keywords = await self.generate_search_keywords(patent_title, client_title, visual_description)
            result["stage_results"]["keywords"] = keywords

            # Stage 2: Perform text queries
            text_result = await self.perform_text_queries(keywords)
            result["stage_results"]["text_queries"] = text_result

            if not text_result["success"]:
                result["error"] = f"Text queries failed: {text_result.get('error', 'Unknown error')}"
                return result

            # Stage 3: Perform image queries
            image_result = await self.perform_image_queries(product_images)
            result["stage_results"]["image_queries"] = image_result

            if not image_result["success"]:
                result["error"] = f"Image queries failed: {image_result.get('error', 'Unknown error')}"
                return result

            # Stage 4: Create union and cross-score
            union_candidates = await self.create_union_and_cross_score(
                text_result["text_pool"],
                image_result["image_pool"]
            )
            result["total_candidates_found"] = len(union_candidates)

            if not union_candidates:
                result["error"] = "No candidates found in union"
                return result

            # Stage 5: Fetch patent data
            enriched_candidates = await self.fetch_patent_data_for_candidates(union_candidates)

            # Stage 6: Scoring pipeline
            candidates = self.normalize_scores(enriched_candidates)
            candidates = await self.calculate_article_match_bonus(asserted_patent_data, candidates)
            candidates = self.calculate_age_bonus(asserted_patent_data, candidates)
            candidates = self.fuse_scores(candidates)

            # Stage 7: Deduplication and diversification
            candidates = await self.deduplicate_and_diversify(candidates)

            # Stage 8: LLM persuasion scoring
            candidates = await self.llm_persuasion_scoring(candidates, asserted_patent_data)

            # Stage 9: Select final shortlist
            shortlist, close_prior_art = self.select_final_shortlist(candidates)

            # Stage 10: Fetch and save assets
            shortlist_with_assets = await self.fetch_and_save_prior_art_assets(shortlist)

            # Finalize results
            result["shortlist"] = shortlist_with_assets
            result["close_prior_art"] = close_prior_art
            result["success"] = True

            end_time = datetime.now()
            result["processing_time"] = (end_time - start_time).total_seconds()

            log_message(f"Automated prior art retrieval completed: {len(shortlist)} candidates, {len(close_prior_art)} close prior art", level='INFO')

            return result

        except Exception as e:
            log_message(f"Error in automated prior art retrieval: {e}", level='ERROR')
            result["error"] = str(e)
            result["success"] = False
            return result
