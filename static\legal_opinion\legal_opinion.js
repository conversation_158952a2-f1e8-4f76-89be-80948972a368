// Legal Opinion Form JavaScript
// Uses Alpine.js for reactive components and htmx for async operations

// Define the Alpine.js data function
document.addEventListener('alpine:init', () => {
    Alpine.data('legalOpinion', () => ({
        // Form sections state
        sections: {
            complaint: { completed: false, expanded: true },
            product: { completed: false, expanded: false },
            priorArt: { completed: false, expanded: false }
        },
        
        // Form data
        formData: {
            // Complaint details (merged from matter & contacts, complaint, and patent)
            storeName: '',
            platform: '',
            asins: [],
            patentNumber: '',

            // Product details (merged with client IP)
            productDescription: '',
            clientPatents: [],
            firstPublicDisplayDate: '',
            firstPublicDisplayDateUnknown: false,
            firstSaleDate: '',
            firstSaleDateUnknown: false,

            // Prior art
            priorArt: []
        },
        
        // File uploads
        files: {
            complaint: [],
            productImages: [],
            commercializationDocs: [],
            clientPatentDocs: [],
            priorArtFiles: []
        },
        
        // Validation state
        validation: {},
        
        // UI state
        activeTooltip: null,
        autosaveStatus: 'saved', // 'saving', 'saved', 'error'
        
        // Methods
        init() {
            // This function can be called from x-init
            console.log('Alpine component initialized');
            this.loadSavedProgress();
            
            // Set up a watcher for autosaving form data
            this.$watch('formData', Alpine.debounce(() => {
                this.triggerAutosave();
            }, 500));
        },
        
        getSectionTitle(key) {
            const titles = {
                complaint: 'Complaint Details',
                product: 'Accused Product',
                priorArt: 'Known Prior Art'
            };
            return titles[key] || key;
        },
        
        toggleSection(sectionKey) {
            this.sections[sectionKey].expanded = !this.sections[sectionKey].expanded;
        },
        
        markSectionCompleted(sectionKey) {
            this.sections[sectionKey].completed = true;
            // Auto-expand next section
            const sectionKeys = Object.keys(this.sections);
            const currentIndex = sectionKeys.indexOf(sectionKey);
            if (currentIndex < sectionKeys.length - 1) {
                const nextSection = sectionKeys[currentIndex + 1];
                this.sections[nextSection].expanded = true;
            }
        },

        gotoSection(sectionKey) {
            // Collapse all sections
            Object.keys(this.sections).forEach(key => {
                this.sections[key].expanded = false;
            });
            // Expand the target section
            this.sections[sectionKey].expanded = true;
            // Scroll to the section
            this.$nextTick(() => {
                document.getElementById(sectionKey).scrollIntoView({ behavior: 'smooth', block: 'start' });
            });
        },
        
        toggleTooltip(field, event) {
            event.stopPropagation();
            this.activeTooltip = this.activeTooltip === field ? null : field;
        },
        
        addArrayItem(arrayPath, item = {}) {
            const array = this.getNestedValue(arrayPath);
            if (Array.isArray(array)) {
                array.push(item);
            }
        },
        
        removeArrayItem(arrayPath, index) {
            const array = this.getNestedValue(arrayPath);
            if (Array.isArray(array)) {
                array.splice(index, 1);
            }
        },
        
        getNestedValue(path) {
            return path.split('.').reduce((obj, key) => obj && obj[key], this);
        },
        
        setNestedValue(path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            const target = keys.reduce((obj, key) => obj[key], this);
            target[lastKey] = value;
        },
        
        // File handling
        handleFileUpload(category, files, patentIndex = null) {
            const allowedTypes = this.getAllowedFileTypes(category);
            const maxSize = this.getMaxFileSize(category);
            
            for (let file of files) {
                if (!this.validateFileType(file, allowedTypes)) {
                    alert(`File ${file.name} is not an allowed type for ${category}`);
                    continue;
                }
                
                if (!this.validateFileSize(file, maxSize)) {
                    alert(`File ${file.name} exceeds maximum size of ${maxSize / 1024 / 1024}MB`);
                    continue;
                }
                
                const fileData = {
                    file: file,
                    name: file.name,
                    size: file.size,
                    type: file.type,
                    id: Date.now() + Math.random()
                };

                if (category === 'clientPatentDocs' && patentIndex !== null) {
                    this.formData.clientPatents[patentIndex].file = fileData;
                } else {
                    this.files[category].push(fileData);
                }
            }
            
            this.triggerAutosave();
        },
        
        removeFile(category, fileId) {
            this.files[category] = this.files[category].filter(f => f.id !== fileId);
            this.triggerAutosave();
        },
        
        getAllowedFileTypes(category) {
            const typeMap = {
                complaint: ['application/pdf', 'message/rfc822', 'text/html', 'image/png', 'image/jpeg'],
                productImages: ['image/png', 'image/jpeg'],
                commercializationDocs: ['application/pdf', 'image/png', 'image/jpeg'],
                clientPatentDocs: ['application/pdf'],
                priorArtFiles: ['application/pdf', 'image/png', 'image/jpeg']
            };
            return typeMap[category] || [];
        },
        
        getMaxFileSize(category) {
            return 25 * 1024 * 1024; // 25MB for all categories
        },
        
        validateFileType(file, allowedTypes) {
            return allowedTypes.includes(file.type);
        },
        
        validateFileSize(file, maxSize) {
            return file.size <= maxSize;
        },
        
        // Autosave functionality
        triggerAutosave() {
            if (this.autosaveTimeout) {
                clearTimeout(this.autosaveTimeout);
            }
            
            this.autosaveStatus = 'saving';
            this.autosaveTimeout = setTimeout(() => {
                this.performAutosave();
            }, 500);
        },
        
        async performAutosave() {
            try {
                const response = await fetch('/legal_opinion/autosave', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        formData: this.formData,
                        sections: this.sections
                    })
                });
                
                if (response.ok) {
                    this.autosaveStatus = 'saved';
                } else {
                    this.autosaveStatus = 'error';
                }
            } catch (error) {
                console.error('Autosave failed:', error);
                this.autosaveStatus = 'error';
            }
        },
        
        // Form submission
        async submitForm() {
            // Validate all required fields
            if (!this.validateAllFields()) {
                alert('Please complete all required fields before submitting.');
                return;
            }
            
            // Create FormData for file uploads
            const formData = new FormData();
            formData.append('data', JSON.stringify(this.formData));
            
            // Add files
            for (const [category, files] of Object.entries(this.files)) {
                files.forEach((fileObj, index) => {
                    formData.append(`${category}_${index}`, fileObj.file);
                });
            }
            
            try {
                const response = await fetch('/legal_opinion/submit', {
                    method: 'POST',
                    body: formData
                });
                
                if (response.ok) {
                    const result = await response.json();
                    alert('Legal opinion request submitted successfully!');
                    // Optionally redirect or reset form
                } else {
                    const error = await response.json();
                    alert(`Submission failed: ${error.message}`);
                }
            } catch (error) {
                console.error('Submission failed:', error);
                alert('Submission failed. Please try again.');
            }
        },
        
        validateAllFields() {
            // Check required fields for simplified form
            const required = [
                'formData.storeName',
                'formData.platform',
                'formData.patentNumber',
                'formData.productDescription'
            ];

            // Check that product images are uploaded
            const hasProductImages = this.files.productImages.length > 0;

            // Check basic required fields
            const fieldsValid = required.every(path => {
                const value = this.getNestedValue(path);
                return value !== '' && value !== false && value !== null && value !== undefined;
            });

            // Check Amazon-specific requirements
            let amazonRequirementsValid = true;
            if (this.formData.platform === 'amazon') {
                amazonRequirementsValid = this.formData.asins.length > 0;
            }

            return fieldsValid && hasProductImages && amazonRequirementsValid;
        },
        
        loadSavedProgress() {
            const saved = localStorage.getItem('legalOpinionProgress');
            if (saved) {
                try {
                    const data = JSON.parse(saved);
                    // Only load if saved within last 24 hours
                    if (Date.now() - data.timestamp < 24 * 60 * 60 * 1000) {
                        if (confirm('Found saved progress from a previous session. Would you like to restore it?')) {
                            Object.assign(this.sections, data.sections);
                            Object.assign(this.formData, data.formData);
                        }
                    }
                } catch (e) {
                    console.error('Error loading saved progress:', e);
                }
            }
        },
        
        updateFormProgress() {
            if (!this.sections) return;
            const completed = Object.values(this.sections).filter(s => s.completed).length;
            const total = Object.keys(this.sections).length;
            const percentage = Math.round((completed / total) * 100);
            document.title = `Legal Opinion (${percentage}% complete) - Maidalv API Studio`;
        }
    }));
});


document.addEventListener('DOMContentLoaded', function() {
    
    // Keyboard accessibility for tooltips
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const alpineEl = document.querySelector('[x-data="legalOpinion"]');
            if (alpineEl && alpineEl.__x) {
                alpineEl.__x.getUnobservedData().activeTooltip = null;
            }
        }
    });
    
    // Enhanced drag and drop visual feedback
    document.addEventListener('dragover', function(e) {
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone) {
            e.preventDefault();
            dropZone.classList.add('dragover');
        }
    });

    document.addEventListener('dragleave', function(e) {
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone && !dropZone.contains(e.relatedTarget)) {
            dropZone.classList.remove('dragover');
        }
    });

    document.addEventListener('drop', function(e) {
        const dropZone = e.target.closest('.file-upload-area');
        if (dropZone) {
            e.preventDefault();
            dropZone.classList.remove('dragover');
        }
    });

    // Warn before leaving with unsaved changes
    window.addEventListener('beforeunload', function(e) {
        const alpineEl = document.querySelector('[x-data="legalOpinion"]');
        if (alpineEl && alpineEl.__x) {
            const status = alpineEl.__x.getUnobservedData().autosaveStatus;
            if (status === 'saving' || status === 'error') {
                const confirmationMessage = 'You have unsaved changes. Are you sure you want to leave?';
                e.returnValue = confirmationMessage;
                return confirmationMessage;
            }
        }
    });

    // Enhanced error handling
    window.addEventListener('error', function(e) {
        console.error('JavaScript error:', e.error || e.message);
    });

    // Performance monitoring
    window.addEventListener('load', function() {
        setTimeout(() => {
            if (performance && performance.getEntriesByType) {
                const perfData = performance.getEntriesByType('navigation');
                if (perfData) {
                    console.log('Page load time:', perfData.loadEventEnd - perfData.domComplete, 'ms');
                }
            }
        }, 0);
    });

    // Handle htmx validation responses
    document.body.addEventListener('htmx:afterRequest', function(evt) {
        if (evt.detail.xhr.status === 200) {
            try {
                const response = JSON.parse(evt.detail.xhr.responseText);
                const fieldName = evt.detail.requestConfig.parameters.field;
                const validationDiv = document.getElementById(fieldName + '-validation');
                
                if (validationDiv) {
                    validationDiv.textContent = response.message;
                    validationDiv.className = 'validation-message ' + (response.valid ? 'success' : 'error');
                }
            } catch (e) {
                console.error('Error parsing htmx response:', e);
            }
        }
    });
});
