from datetime import datetime, timedelta
import sqlite3, time, functools, json, jwt, pytz
from logdata import db_path, init_db
import pandas as pd
from Alerts.zz_Process_Wrapper import run_update_task
from DatabaseManagement.ImportExport import get_table_from_GZ
from flask import Flask, render_template, request, redirect, url_for, session, Response, jsonify
from waitress import serve

# Import the visualizer API, plaintiff review API, plaintiff management, and TRO IP
from ui_controllers.app_visualizer import init_visualizer_routes
from ui_controllers.app_plaintiff_review import init_plaintiff_review_routes
from ui_controllers.app_plaintiffs import init_plaintiffs_routes
from ui_controllers.app_tro_ip import init_tro_ip_routes
from ui_controllers.app_boundingbox import init_boundingbox_routes # Import bounding box routes
from ui_controllers.app_statistics import init_statistics_routes # Import statistics routes
from ui_controllers.app_api_keys import init_api_keys_routes # Import API keys routes

# Import cache manager to ensure it's loaded on startup and accessible
# And import the refresh function directly
from ui_controllers.cache_manager import refresh_cached_data

app = Flask(__name__)
app.secret_key = 'AeRthskhg683496^59475009fjekt'  # Replace with a secure secret key
app.config['JWT_SECRET_KEY'] = 'AeRthskhg683496^59475009fjekt879846'  # Change this to a secure key
app.config['JWT_ACCESS_TOKEN_EXPIRES'] = timedelta(hours=24)

# Import authentication decorator
from ui_controllers.auth_decorators import login_required

# Initialize the visualizer API, plaintiff review API, plaintiff management, and TRO IP
app = init_visualizer_routes(app)
# Corrected function name from the import
app = init_plaintiff_review_routes(app)
app = init_plaintiffs_routes(app)
app = init_tro_ip_routes(app)
app = init_boundingbox_routes(app) # Initialize bounding box routes
app = init_statistics_routes(app) # Initialize statistics routes
app = init_api_keys_routes(app) # Initialize API keys routes

# import the Celery instance (already auto‑wrapped with app_context)
from Scheduler_Celery.celery_app import celery_app as celery

def token_required(f):
    @functools.wraps(f)
    def decorated(*args, **kwargs):
        token = None

        # Check if token is in headers
        if 'Authorization' in request.headers:
            auth_header = request.headers['Authorization']
            try:
                token = auth_header.split(" ")[1]  # Bearer <token>
            except IndexError:
                app.logger.info(f"Invalid token format in request: {request}") # Log the request
                return jsonify({'message': 'Invalid token format'}), 401

        if not token:
            app.logger.info(f"Token is missing in request: {request}") # Log the request
            return jsonify({'message': 'Token is missing'}), 401

        try:
            # Verify token
            data = jwt.decode(token, app.config['JWT_SECRET_KEY'], algorithms=['HS256'])
            current_user = data['user']
        except jwt.ExpiredSignatureError:
            app.logger.info(f"Token has expired in request: {request}") # Log the request
            return jsonify({'message': 'Token has expired'}), 401
        except jwt.InvalidTokenError:
            app.logger.info(f"Invalid token in request: {request}") # Log the request
            return jsonify({'message': 'Invalid token'}), 401

        return f(*args, **kwargs)
    return decorated

# Login route
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        if username == 'serge' and password == 'maidalv888':  # Hardcoded credentials
            session['logged_in'] = True
            return redirect(url_for('visualizer'))
        else:
            return render_template('login.html', error='Invalid credentials')
    return render_template('login.html')

# create token for product 3
@app.route('/authenticate', methods=['POST'])
def authenticate():
    data = request.get_json()
    username = data.get('username')
    password = data.get('password')

    if username == 'java_springboot' and password == '%6wjfhtf84687DFSJTfde484':  # Replace with proper authentication
        token = jwt.encode({
            'user': username,
            'exp': datetime.utcnow() + app.config['JWT_ACCESS_TOKEN_EXPIRES']
        }, app.config['JWT_SECRET_KEY'], algorithm='HS256')

        return jsonify({
            'status': 'success',
            'token': token
        })
    else:
        return jsonify({
            'status': 'error',
            'message': 'Invalid credentials'
        }), 401

# Logout route
@app.route('/logout')
def logout():
    session.pop('logged_in', None)
    return redirect(url_for('login'))


@app.route('/start_update_task', methods=['POST'])
@login_required
def start_update_task():
    try:
        threshold_days = request.json.get('loopBackDays')

        result = run_update_task(threshold_days)

        if result.get('error'):
            return jsonify(result), 500
        elif result.get('status') == 'failed_to_start':
            return jsonify(result), 500
        else:
            return jsonify(result)

    except Exception as e:
        print(f"Error in start_update_task: {str(e)}")
        return jsonify({'error': str(e)}), 500

@app.route('/visualizer')
@login_required
def visualizer():
    """Render the case data visualizer page"""
    return render_template('visualizer.html')

@app.route('/')
@login_required
def index():
    """Redirect root to visualizer"""
    return redirect(url_for('visualizer'))

@app.route('/plaintiff_duplicates')
@login_required
def plaintiff_duplicates():
    """Render the case data visualizer page"""
    return render_template('plaintiff_duplicates.html')

@app.route('/plaintiffs')
@login_required
def plaintiffs():
    """Render the plaintiffs management page"""
    return render_template('plaintiffs.html')


@app.route('/celery_scheduler/task-status/<task_id>')
def task_status(task_id):
    """Check the status of a Celery task"""

    task = celery.AsyncResult(task_id)
    return jsonify({
        'task_id': task_id,
        'status': task.status,
        'result': task.result if task.ready() else None,
        'info': task.info
    })

@app.route('/celery_scheduler/info')
def scheduler_info():
    """Show scheduler configuration"""
    try:
        config = celery.conf
        beat_schedule_serializable = {}
        for task_name, task_config in config.get('beat_schedule', {}).items():
            schedule = task_config.get('schedule')
            if hasattr(schedule, 'hour') and hasattr(schedule, 'minute'):
                schedule_str = f"crontab(hour={schedule.hour}, minute={schedule.minute})"
                if hasattr(schedule, 'day_of_week') and schedule.day_of_week:
                    schedule_str = f"crontab(hour={schedule.hour}, minute={schedule.minute}, day_of_week={schedule.day_of_week})"
                if hasattr(schedule, 'day_of_month') and schedule.day_of_month:
                    schedule_str = f"crontab(hour={schedule.hour}, minute={schedule.minute}, day_of_month={schedule.day_of_month})"
            else:
                schedule_str = str(schedule)
            
            beat_schedule_serializable[task_name] = {
                'task': task_config.get('task'),
                'schedule': schedule_str,
                'options': task_config.get('options', {})
            }
        
        return jsonify({
            'beat_schedule': beat_schedule_serializable,
            'timezone': config.get('timezone', 'Unknown'),
            'broker_url': config.get('broker_url', 'Unknown'),
            'status': 'Scheduler active'
        })
    except Exception as e:
        return jsonify({
            'error': str(e),
            'status': 'Error getting scheduler info'
        }), 500


@app.route('/celery_scheduler/trigger-daily-fetch')
def trigger_daily_fetch():
    """Manually trigger the daily fetch task (for testing)"""
    from Scheduler_Celery.tasks import daily_case_fetch_task
    task = daily_case_fetch_task.delay()
    return jsonify({
        'message': 'Daily fetch task triggered manually',
        'task_id': task.id
    })
# @app.route('/plaintiff_review')
# @login_required
# def plaintiff_review():
#     """Render the review plaintiff page"""
#     return render_template('plaintiff_review.html')

if __name__ == '__main__':
    init_db()

    # from Check.Do_Check import download_and_check # Only for the main app.py run, not for spawn processes
    # from Check.RAG.RAG_Inference import load_embedding_models
    # load_embedding_models()  # Load the tensor flow model and other variables. Only for the main app.py run, not for spawn processes

    # from Check.Data_Cache import update_dataframe_cache
    # update_dataframe_cache()

    # from apscheduler.schedulers.background import BackgroundScheduler
    # from apscheduler.triggers.cron import CronTrigger
    # from Statistics.ScheduledTasks import daily_statistics_collection, weekly_statistics_cleanup

    # scheduler = BackgroundScheduler(timezone=pytz.UTC)
    # scheduler.add_job(func=run_fetch_task, trigger=CronTrigger(hour=02, minute=0, timezone=pytz.timezone('America/New_York')))
    # scheduler.add_job(func=run_update_task, trigger=CronTrigger(hour=21, minute=0, timezone=pytz.timezone('America/New_York')))
    # scheduler.add_job(func=run_fix_task, trigger=CronTrigger(hour=23, minute=0, timezone=pytz.timezone('America/New_York')))

    # Add daily statistics collection task (runs at 6 AM EST)
    # scheduler.add_job(
    #     func=daily_statistics_collection,
    #     trigger=CronTrigger(hour=6, minute=0, timezone=pytz.timezone('America/New_York')),
    #     id='daily_statistics_collection',
    #     name='Daily Statistics Collection'
    # )

    # # Add weekly statistics cleanup task (runs every Sunday at 2 AM EST)
    # scheduler.add_job(
    #     func=weekly_statistics_cleanup,
    #     trigger=CronTrigger(day_of_week=6, hour=2, minute=0, timezone=pytz.timezone('America/New_York')),
    #     id='weekly_statistics_cleanup',
    #     name='Weekly Statistics Cleanup'
    # )

    # scheduler.start()



    refresh_cached_data(force=False)

    serve(
        app,
        host="0.0.0.0",
        port=5000,
        threads=10,  # The number of requests Waitress can actively process at the same time. This is your primary concurrency control.
        connection_limit=40,  # The maximum number of connections Waitress will accept, including those being actively processed and those waiting in its internal queue.
        channel_timeout=900
    )
    # run_id = create_new_run()
    # print(os.environ["LN_chrome_folder"])
    # get_cases(run_id, '2024-11-22', 1)
