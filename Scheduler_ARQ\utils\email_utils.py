import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON>Multipart
from langfuse import observe
import logging

logger = logging.getLogger(__name__)

@observe(name="Send Error Notification Email", capture_input=False, capture_output=False)
def send_error_notification_email(error_msg, task_name="ARQ Task"):
    try:
        # Create message
        msg = MIMEMultipart()
        msg['From'] = '<EMAIL>'
        msg['To'] = '<EMAIL>, <EMAIL>'
        msg['Subject'] = f"🚨 {task_name} - FAILED"
        
        body = f"""
        Task: {task_name}
        Status: FAILED
        Error: {error_msg}
        Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        # Send email (implement your SMTP settings)
        server = smtplib.SMTP('smtp.gmail.com', 587)
        server.starttls()
        # Add your email credentials here
        server.login('<EMAIL>', 'your_password')
        text = msg.as_string()
        server.sendmail('<EMAIL>', ['<EMAIL>', '<EMAIL>'], text)
        server.quit()
        
        logger.info(f"Error notification email sent for {task_name}")
        
    except Exception as e:
        logger.error(f"Failed to send error notification email: {str(e)}")

def send_maijiazhichi_report_email(stats):
    """Send Maijiazhichi report email"""
    try:
        # Implement your email sending logic here
        logger.info("Maijiazhichi report email sent")
    except Exception as e:
        logger.error(f"Failed to send Maijiazhichi report email: {str(e)}")