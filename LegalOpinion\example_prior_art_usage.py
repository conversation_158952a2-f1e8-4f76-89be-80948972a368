"""
Example usage of the Automated Prior Art Retrieval System

This script demonstrates how to use the automated prior art retrieval system
in a legal opinion workflow.
"""

import os
import sys
import asyncio
import tempfile
from pathlib import Path

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from LegalOpinion.automated_prior_art_retrieval import AutomatedPriorArtRetrieval
from LegalOpinion.patent_processor import PatentProcessor
from logdata import log_message


async def example_automated_prior_art_retrieval():
    """
    Example of how to use the automated prior art retrieval system.
    """
    log_message("Starting example automated prior art retrieval", level='INFO')
    
    # Sample asserted patent data (would come from database in real usage)
    asserted_patent_data = {
        "id": "sample-uuid",
        "reg_no": "D640227",
        "patent_title": "Storage basket",
        "date_published": "2011-06-21",
        "loc_code": "06-02",
        "uspc_class": "D6",
        "uspc_subclass": "402",
        "folder": "sample-folder",
        "fig_files": ["fig1.png", "fig2.png"],
        "inventors": "<PERSON>",
        "assignee": "Sample Company",
        "abstract": "A storage basket with ornamental design features"
    }
    
    # Sample product images (would be uploaded by user in real usage)
    with tempfile.TemporaryDirectory() as temp_dir:
        # Create sample product image files
        product_images = []
        for i in range(3):
            image_path = os.path.join(temp_dir, f"product_view_{i}.jpg")
            # In real usage, these would be actual image files
            with open(image_path, 'wb') as f:
                f.write(b'sample image data')
            product_images.append(image_path)
        
        # Initialize the automated prior art retrieval system
        prior_art_dir = os.path.join(temp_dir, "prior_art")
        retrieval_system = AutomatedPriorArtRetrieval(
            submission_id="example-submission-001",
            prior_art_dir=prior_art_dir
        )
        
        try:
            # Perform automated prior art retrieval
            log_message("Performing automated prior art retrieval...", level='INFO')
            
            result = await retrieval_system.retrieve_automated_prior_art(
                asserted_patent_data=asserted_patent_data,
                product_images=product_images,
                visual_description="A woven storage container with handles",
                client_title="Client storage basket design"
            )
            
            # Process results
            if result["success"]:
                log_message(f"✅ Prior art retrieval successful!", level='INFO')
                log_message(f"   Total candidates found: {result['total_candidates_found']}", level='INFO')
                log_message(f"   Shortlist size: {len(result['shortlist'])}", level='INFO')
                log_message(f"   Close prior art: {len(result['close_prior_art'])}", level='INFO')
                log_message(f"   Processing time: {result['processing_time']:.2f} seconds", level='INFO')
                
                # Display shortlist
                log_message("\n📋 Prior Art Shortlist:", level='INFO')
                for i, candidate in enumerate(result['shortlist'][:5], 1):  # Show top 5
                    log_message(f"   {i}. {candidate['reg_no']} - {candidate.get('title', 'N/A')}", level='INFO')
                    log_message(f"      LLM Score: {candidate.get('llm_persuasion_score', 0):.1f}/100", level='INFO')
                    log_message(f"      Fused Score: {candidate.get('fused_score', 0):.3f}", level='INFO')
                    log_message(f"      Article Bonus: {candidate.get('article_bonus', 0):.2f}", level='INFO')
                    log_message(f"      Age Bonus: {candidate.get('age_bonus', 0):.2f}", level='INFO')
                
                # Display close prior art
                if result['close_prior_art']:
                    log_message("\n🎯 Close Prior Art (Egyptian Goddess narrow scope):", level='INFO')
                    for candidate in result['close_prior_art']:
                        log_message(f"   • {candidate['reg_no']} - Score: {candidate.get('llm_persuasion_score', 0):.1f}/100", level='INFO')
                
                # Display stage results
                stage_results = result.get('stage_results', {})
                if 'keywords' in stage_results:
                    log_message(f"\n🔍 Generated Keywords: {' | '.join(stage_results['keywords'])}", level='INFO')
                
                if 'text_queries' in stage_results:
                    text_result = stage_results['text_queries']
                    log_message(f"📝 Text Query Results: {text_result.get('total_candidates', 0)} candidates", level='INFO')
                
                if 'image_queries' in stage_results:
                    image_result = stage_results['image_queries']
                    log_message(f"🖼️  Image Query Results: {image_result.get('total_candidates', 0)} candidates", level='INFO')
                
            else:
                log_message(f"❌ Prior art retrieval failed: {result.get('error', 'Unknown error')}", level='ERROR')
                
        except Exception as e:
            log_message(f"❌ Error in example: {e}", level='ERROR')
            raise


async def example_patent_processor_integration():
    """
    Example of how to use the automated prior art retrieval through PatentProcessor.
    """
    log_message("Starting PatentProcessor integration example", level='INFO')
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # Set up workspace paths
        workspace_paths = {
            "asserted_patent": os.path.join(temp_dir, "asserted_patent"),
            "client_patents": os.path.join(temp_dir, "client_patents"),
            "product_images": os.path.join(temp_dir, "product_images"),
            "prior_art": os.path.join(temp_dir, "prior_art"),
            "manifests": os.path.join(temp_dir, "manifests")
        }
        
        # Create directories
        for path in workspace_paths.values():
            os.makedirs(path, exist_ok=True)
        
        # Create sample product images
        product_images_dir = workspace_paths["product_images"]
        for i in range(2):
            image_path = os.path.join(product_images_dir, f"product_{i}.jpg")
            with open(image_path, 'wb') as f:
                f.write(b'sample product image data')
        
        # Initialize PatentProcessor
        processor = PatentProcessor(
            submission_id="example-integration-001",
            workspace_paths=workspace_paths
        )
        
        # Sample asserted patent data
        asserted_patent_data = {
            "reg_no": "D640227",
            "patent_title": "Storage basket",
            "date_published": "2011-06-21",
            "loc_code": "06-02",
            "uspc_class": "D6"
        }
        
        try:
            # Perform automated prior art retrieval through PatentProcessor
            log_message("Performing automated prior art retrieval via PatentProcessor...", level='INFO')
            
            result = await processor.perform_automated_prior_art_retrieval(
                asserted_patent_data=asserted_patent_data,
                product_images_dir=product_images_dir,
                visual_description="A woven storage container with decorative pattern",
                client_title="Client basket design"
            )
            
            if result["success"]:
                log_message(f"✅ PatentProcessor integration successful!", level='INFO')
                log_message(f"   Shortlist: {len(result['shortlist'])} candidates", level='INFO')
                log_message(f"   Close prior art: {len(result['close_prior_art'])} candidates", level='INFO')
                log_message(f"   Evidence entries: {result['evidence_entries']}", level='INFO')
            else:
                log_message(f"❌ PatentProcessor integration failed: {result.get('error', 'Unknown error')}", level='ERROR')
                
        except Exception as e:
            log_message(f"❌ Error in PatentProcessor integration: {e}", level='ERROR')
            raise
        finally:
            # Clean up
            await processor.cleanup()


def print_system_overview():
    """Print an overview of the automated prior art retrieval system."""
    overview = """
🏛️  AUTOMATED PRIOR ART RETRIEVAL SYSTEM
========================================

This system implements the Egyptian Goddess standard for design patent prior art analysis:

📋 PIPELINE STAGES:
1. LLM Keyword Generation - Generate 3 search terms from patent title & visual description
2. Text Queries - Semantic search using BGE embeddings in Patent_Title_bge_small collection  
3. Image Queries - Visual similarity using SigLIP embeddings in IP_Assets_Optimized collection
4. Union & Cross-scoring - Combine results and fill missing modality scores
5. Score Normalization - Min-max scaling per modality
6. Article Matching - LLM-based Curver legal standard comparison (0-10 scale)
7. Age Bonus - Older patents get higher scores (0.2 * years_delta, capped at 1.0)
8. Score Fusion - Weighted combination: 0.55*img + 0.35*text + 0.2*bonuses
9. Deduplication - DBSCAN clustering + article subtype diversification (max 3 per LOC code)
10. LLM Persuasion Scoring - 100-point rubric for final ranking
11. Asset Fetching - Download figures from USPTO_Grants and create metadata

🎯 CLOSE PRIOR ART SELECTION:
- Keep 3 best if 3 are above 80 points
- Keep 2 if best > 60 and second within 10% 
- Else keep only 1 (the best)

📊 SCORING RUBRIC (100 points total):
- Article match (Curver): 25 pts
- Closeness to asserted design: 25 pts  
- Age advantage: 15 pts
- Figure usefulness: 15 pts
- Diversity potential: 10 pts
- Simplicity: 10 pts

🔧 CONFIGURATION:
- Text pool cap: 60 candidates
- Image pool cap: 80 candidates  
- Final shortlist: 20 candidates
- Close prior art threshold: 80 points
"""
    print(overview)


async def main():
    """Main function to run examples."""
    print_system_overview()
    
    log_message("🚀 Starting Automated Prior Art Retrieval Examples", level='INFO')
    
    try:
        # Run the basic example
        await example_automated_prior_art_retrieval()
        
        log_message("\n" + "="*50, level='INFO')
        
        # Run the PatentProcessor integration example
        await example_patent_processor_integration()
        
        log_message("\n✅ All examples completed successfully!", level='INFO')
        
    except Exception as e:
        log_message(f"❌ Example failed: {e}", level='ERROR')
        raise


if __name__ == "__main__":
    asyncio.run(main())
