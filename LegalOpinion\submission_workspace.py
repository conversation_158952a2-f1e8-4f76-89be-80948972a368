"""
Submission Workspace Manager

Handles creation and management of submission workspaces for legal opinion processing.
Each submission gets a self-contained workspace with normalized data and exhibits.
"""

import os
import json
import hashlib
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any
from logdata import log_message


class SubmissionWorkspaceManager:
    """
    Manages submission workspaces for legal opinion processing.
    
    Creates and maintains the following structure:
    /Documents/LegalOpinion/{submission_id}/
      asserted_patent/           # PDF + figures + db-metadata.json
      client_patents/            # PDF + figures + db-metadata.json per patent
      product_images/            # uploaded client images
      prior_art/                 # PDFs/images copied from USPTO_Grants
      manifests/
        evidence_manifest.json   # exhibits with hashes, provenance
        collected_case.json      # single JSON for downstream pipeline
      logs/
      tmp/
    """
    
    def __init__(self, base_path: Optional[str] = None):
        """
        Initialize the workspace manager.
        
        Args:
            base_path: Base path for legal opinion workspaces. 
                      Defaults to ../Documents/LegalOpinion/
        """
        if base_path is None:
            # Default to Documents/LegalOpinion relative to current working directory
            base_path = os.path.abspath(os.path.join(os.getcwd(), "..", "Documents", "LegalOpinion"))
        
        self.base_path = Path(base_path)
        self.version = "1.0.0"
    
    def create_submission_workspace(self, submission_id: str) -> Dict[str, str]:
        """
        Create a submission workspace with all required subdirectories.
        
        Args:
            submission_id: Unique identifier for the submission (e.g., LO-2EDDD671)
            
        Returns:
            Dict with paths to all created directories
        """
        workspace_path = self.base_path / submission_id
        
        # Define all required subdirectories
        subdirs = [
            "asserted_patent",
            "client_patents", 
            "product_images",
            "prior_art",
            "manifests",
            "logs",
            "tmp"
        ]
        
        # Create workspace root and subdirectories idempotently
        workspace_path.mkdir(parents=True, exist_ok=True)
        
        paths = {"workspace": str(workspace_path)}
        
        for subdir in subdirs:
            subdir_path = workspace_path / subdir
            subdir_path.mkdir(exist_ok=True)
            paths[subdir] = str(subdir_path)
        
        # Write README.txt with build version and timestamp
        readme_path = workspace_path / "README.txt"
        readme_content = f"""Legal Opinion Submission Workspace
Submission ID: {submission_id}
Created: {datetime.now().isoformat()}
Build Version: {self.version}
Workspace Structure:
- asserted_patent/: Patent PDF, figures, and database metadata
- client_patents/: Client-owned patent documents and metadata  
- product_images/: Uploaded product images from client
- prior_art/: Prior art documents from USPTO_Grants
- manifests/: Evidence manifest and collected case data
- logs/: Processing logs
- tmp/: Temporary files
"""
        
        with open(readme_path, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        
        log_message(f"Created submission workspace: {workspace_path}", level='INFO')
        
        return paths
    
    def get_workspace_path(self, submission_id: str) -> Path:
        """Get the path to a submission workspace."""
        return self.base_path / submission_id
    
    def workspace_exists(self, submission_id: str) -> bool:
        """Check if a submission workspace exists."""
        return self.get_workspace_path(submission_id).exists()
    
    def get_subdir_path(self, submission_id: str, subdir: str) -> Path:
        """Get the path to a specific subdirectory within a workspace."""
        return self.get_workspace_path(submission_id) / subdir
    
    def create_patent_subdir(self, submission_id: str, reg_no: str, parent_dir: str = "client_patents") -> Path:
        """
        Create a subdirectory for a specific patent within client_patents or asserted_patent.
        
        Args:
            submission_id: The submission ID
            reg_no: Patent registration number (e.g., D640227)
            parent_dir: Parent directory name (client_patents or asserted_patent)
            
        Returns:
            Path to the created patent subdirectory
        """
        if parent_dir == "asserted_patent":
            # For asserted patent, don't create a subdirectory
            patent_dir = self.get_subdir_path(submission_id, parent_dir)
        else:
            # For client patents, create subdirectory by reg_no
            parent_path = self.get_subdir_path(submission_id, parent_dir)
            patent_dir = parent_path / reg_no
        
        patent_dir.mkdir(parents=True, exist_ok=True)
        return patent_dir
    
    def save_metadata(self, submission_id: str, subdir: str, filename: str, metadata: Dict[str, Any]) -> str:
        """
        Save metadata as JSON file in the specified subdirectory.
        
        Args:
            submission_id: The submission ID
            subdir: Subdirectory name
            filename: JSON filename (without extension)
            metadata: Metadata dictionary to save
            
        Returns:
            Path to the saved metadata file
        """
        subdir_path = self.get_subdir_path(submission_id, subdir)
        metadata_path = subdir_path / f"{filename}.json"
        
        with open(metadata_path, 'w', encoding='utf-8') as f:
            json.dump(metadata, f, indent=2, default=str)
        
        return str(metadata_path)
    
    def load_metadata(self, submission_id: str, subdir: str, filename: str) -> Optional[Dict[str, Any]]:
        """
        Load metadata from JSON file.
        
        Args:
            submission_id: The submission ID
            subdir: Subdirectory name  
            filename: JSON filename (without extension)
            
        Returns:
            Metadata dictionary or None if file doesn't exist
        """
        subdir_path = self.get_subdir_path(submission_id, subdir)
        metadata_path = subdir_path / f"{filename}.json"
        
        if not metadata_path.exists():
            return None
            
        try:
            with open(metadata_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            log_message(f"Error loading metadata from {metadata_path}: {e}", level='ERROR')
            return None
    
    def compute_file_hash(self, file_path: str) -> str:
        """
        Compute SHA256 hash of a file.
        
        Args:
            file_path: Path to the file
            
        Returns:
            SHA256 hash as hex string
        """
        sha256_hash = hashlib.sha256()
        
        try:
            with open(file_path, "rb") as f:
                # Read file in chunks to handle large files
                for chunk in iter(lambda: f.read(4096), b""):
                    sha256_hash.update(chunk)
            return sha256_hash.hexdigest()
        except IOError as e:
            log_message(f"Error computing hash for {file_path}: {e}", level='ERROR')
            return ""
    
    def log_to_workspace(self, submission_id: str, message: str, level: str = 'INFO'):
        """
        Log a message to the workspace-specific log file.
        
        Args:
            submission_id: The submission ID
            message: Log message
            level: Log level (INFO, WARNING, ERROR)
        """
        logs_dir = self.get_subdir_path(submission_id, "logs")
        log_file = logs_dir / f"{submission_id}.log"
        
        timestamp = datetime.now().isoformat()
        log_entry = f"[{timestamp}] {level}: {message}\n"
        
        with open(log_file, 'a', encoding='utf-8') as f:
            f.write(log_entry)
        
        # Also log to main system log
        log_message(f"[{submission_id}] {message}", level=level)
