/* Legal Opinion Form Styles */

/* Import base styles from api_studio */
@import url('../api_studio/api_studio.css');

/* Legal Opinion specific overrides and additions */
.legal-opinion-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Progressive sections styling */
.form-section {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 24px;
    border-left: 4px solid #1E2442;
    transition: all 0.3s ease;
}

.form-section.collapsed {
    padding: 16px 24px;
}

.form-section.completed {
    border-left-color: #2196F3;
    background: rgba(33, 150, 243, 0.1);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.section-header h3 {
    color: #ffffff;
    font-size: 1.4em;
    margin: 0;
    display: flex;
    align-items: center;
}

.section-number {
    background: #1E2442;
    color: white;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.9em;
    font-weight: bold;
    margin-right: 12px;
}

.section-toggle {
    background: none;
    border: none;
    color: #ffffff;
    font-size: 1.2em;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: background-color 0.2s;
}

.section-toggle:hover {
    background: rgba(255, 255, 255, 0.1);
}

.section-content {
    transition: all 0.3s ease;
}

.section-content.hidden {
    display: none;
}

/* Form field styling */
.form-group {
    margin-bottom: 20px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 6px;
    color: #ffffff;
    font-weight: 500;
    font-size: 0.95em;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #ffffff;
    font-size: 0.95em;
    transition: all 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #1E2442;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(30, 36, 66, 0.2);
}

.form-group select option {
    background: #1E2442;
    color: #ffffff;
}

.form-group input::placeholder,
.form-group textarea::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Required field indicator */
.required {
    color: #ff6b6b;
    font-weight: bold;
    margin-left: 4px;
}

.optional {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.85em;
    font-weight: normal;
    margin-left: 8px;
}

/* Tooltip styling */
.tooltip-container {
    position: relative;
    display: inline-block;
    margin-left: 8px;
}

.info-icon {
    width: 18px;
    height: 18px;
    background: #2196F3;
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
}

.info-icon:hover {
    background: #1976D2;
    transform: scale(1.1);
}

.tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #1a1a1a;
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 8px;
    font-size: 0.85em;
    line-height: 1.4;
    width: 320px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease, transform 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: #1a1a1a;
}

.tooltip.show {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-8px);
}

.tooltip h4 {
    color: #4CAF50;
    margin: 0 0 8px 0;
    font-size: 0.9em;
}

.tooltip p {
    margin: 0 0 8px 0;
}

.tooltip p:last-child {
    margin-bottom: 0;
}

/* File upload styling */
.file-upload-area {
    border: 2px dashed rgba(255, 255, 255, 0.3);
    border-radius: 8px;
    padding: 24px;
    text-align: center;
    background: rgba(255, 255, 255, 0.05);
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #1E2442;
    background: rgba(30, 36, 66, 0.1);
}

.file-upload-area.dragover {
    border-color: #1E2442;
    background: rgba(30, 36, 66, 0.2);
}

.file-upload-input {
    display: none;
}

.file-upload-text {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9em;
}

.file-upload-text strong {
    color: #1E2442;
}

/* File list styling */
.file-list {
    margin-top: 12px;
}

.file-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin-bottom: 8px;
}

.file-item:last-child {
    margin-bottom: 0;
}

.file-name {
    color: #ffffff;
    font-size: 0.9em;
    flex: 1;
    margin-right: 12px;
}

.file-size {
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.8em;
    margin-right: 12px;
}

.file-remove {
    background: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 4px 8px;
    font-size: 0.8em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.file-remove:hover {
    background: #d32f2f;
}

/* Validation styling */
.form-group.valid input,
.form-group.valid select,
.form-group.valid textarea {
    border-color: #1E2442;
}

.form-group.invalid input,
.form-group.invalid select,
.form-group.invalid textarea {
    border-color: #f44336;
}

.validation-message {
    font-size: 0.8em;
    margin-top: 4px;
    padding: 4px 8px;
    border-radius: 4px;
}

.validation-message.success {
    color: #1E2442;
    background: rgba(30, 36, 66, 0.1);
}

.validation-message.error {
    color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

/* Checkbox and radio styling */
.checkbox-group,
.radio-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.checkbox-item,
.radio-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
}

.checkbox-item input[type="checkbox"],
.radio-item input[type="radio"] {
    width: auto;
    margin: 0;
    margin-top: 2px;
}

.checkbox-item label,
.radio-item label {
    margin: 0;
    cursor: pointer;
    flex: 1;
}

/* Multi-select styling */
.multi-select {
    position: relative;
}

.multi-select-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: #1a1a1a;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-top: none;
    border-radius: 0 0 6px 6px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 100;
    display: none;
}

.multi-select-dropdown.show {
    display: block;
}

.multi-select-option {
    padding: 10px 12px;
    cursor: pointer;
    transition: background-color 0.2s;
    display: flex;
    align-items: center;
    gap: 8px;
}

.multi-select-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.multi-select-option.selected {
    background: rgba(30, 36, 66, 0.2);
}

/* Progress indicator */
.progress-indicator {
    position: fixed;
    top: 60px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    padding: 16px;
    border-radius: 8px;
    z-index: 1000;
    min-width: 200px;
}

.progress-item {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 8px;
    font-size: 0.85em;
}

.progress-item:last-child {
    margin-bottom: 0;
}

.progress-icon {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
}

.progress-icon.completed {
    background: #1E2442;
    color: white;
}

.progress-icon.current {
    background: #2196F3;
    color: white;
}

.progress-icon.pending {
    background: rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.6);
}

.progress-link {
    color: #ffffff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.progress-link:hover {
    color: #2196F3;
    text-decoration: underline;
}

/* Loading states */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid transparent;
    border-top: 2px solid #1E2442;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Focus management for accessibility */
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: 2px solid #1E2442;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .form-section {
        border: 2px solid #ffffff;
    }

    .info-icon {
        border: 1px solid #ffffff;
    }

    .tooltip {
        border: 2px solid #ffffff;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .form-section,
    .section-content,
    .tooltip,
    .info-icon {
        transition: none;
    }

    .loading::after {
        animation: none;
    }
}

/* Print styles */
@media print {
    .progress-indicator,
    .autosave-status,
    .section-toggle,
    .info-icon {
        display: none;
    }

    .form-section {
        break-inside: avoid;
        page-break-inside: avoid;
    }

    .section-content {
        display: block !important;
    }
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .tooltip {
        background: #2a2a2a;
        border-color: rgba(255, 255, 255, 0.2);
    }

    .tooltip::after {
        border-top-color: #2a2a2a;
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .legal-opinion-container {
        padding: 12px;
    }

    .form-section {
        padding: 16px;
        margin-bottom: 16px;
    }

    .tooltip {
        width: 280px;
        font-size: 0.8em;
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        bottom: auto;
    }

    .tooltip::after {
        display: none;
    }

    .progress-indicator {
        position: relative;
        top: auto;
        right: auto;
        margin-bottom: 20px;
    }

    .section-header h3 {
        font-size: 1.2em;
    }

    .section-number {
        width: 24px;
        height: 24px;
        font-size: 0.8em;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        font-size: 16px; /* Prevents zoom on iOS */
    }
}

@media (max-width: 480px) {
    .legal-opinion-container {
        padding: 8px;
    }

    .form-section {
        padding: 12px;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .section-toggle {
        align-self: flex-end;
    }
}

.date-group {
    display: flex;
    align-items: center;
    gap: 12px; /* Increased gap for better spacing */
    margin-bottom: 8px;
}

.date-group label:first-of-type {
    flex: 0 0 250px; /* Increased width for the first label */
    margin-right: auto; /* Push other elements to the right */
}

.date-group input[type="date"] {
    flex-grow: 1; /* Prevent date input from growing */
}

.date-group input[type="checkbox"] {
    width: auto; /* Override default input width */
    flex-grow: 0;
}
