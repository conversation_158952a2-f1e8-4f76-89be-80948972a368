#!/bin/bash
### This is the start file for the Docker container

# Save environment variables for SSH sessions
echo "#!/bin/bash" > /etc/profile.d/docker_env.sh
env | grep -v "PATH" | grep -v "PWD" | grep -v "HOME" | grep -v "HOSTNAME" | sed 's/^\([^=]*\)=\(.*\)/export \1="\2"/' >> /etc/profile.d/docker_env.sh
echo 'export PYTHONPATH="/app"' >> /etc/profile.d/docker_env.sh
chmod +x /etc/profile.d/docker_env.sh

# Start Redis server
redis-server --daemonize yes

# Set strict permissions for the private key for SSH sessions
chmod 600 /app/.ssh/id_rsa

# Start SSH server
/usr/sbin/sshd

echo "Starting ARQ Scheduler Worker..."
arq Scheduler_ARQ.worker.SchedulerWorkerSettings &

echo "Starting ARQ Scheduler..."
python -m Scheduler_ARQ.scheduler &

echo "Starting Celery Beat (Scheduler)..."
celery -A Scheduler_Celery.celery_app:celery_app beat --loglevel=info &

celery -A Scheduler_Celery.celery_app:celery_app flower --persistent=True --db=flower.db --basic-auth=serge:maidalv888 &

# Start the main application
cd /app && python app.py
